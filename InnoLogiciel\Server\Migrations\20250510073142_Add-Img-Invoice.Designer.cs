﻿// <auto-generated />
using System;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace InnoLogiciel.Server.Migrations
{
    [DbContext(typeof(InnoLogicielContext))]
    [Migration("20250510073142_Add-Img-Invoice")]
    partial class AddImgInvoice
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("InnoBook.Entities.Attachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<Guid?>("ExpensesId")
                        .HasColumnType("uuid")
                        .HasColumnName("expenses_id");

                    b.Property<string>("Filename")
                        .HasColumnType("text")
                        .HasColumnName("filename");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<string>("Path")
                        .HasColumnType("text")
                        .HasColumnName("path");

                    b.Property<decimal?>("Size")
                        .HasColumnType("numeric")
                        .HasColumnName("size");

                    b.Property<string>("Type")
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_attachment");

                    b.HasIndex("ExpensesId")
                        .HasDatabaseName("ix_attachment_expenses_id");

                    b.HasIndex("InvoiceId")
                        .HasDatabaseName("ix_attachment_invoice_id");

                    b.ToTable("attachment", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Category", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("boolean")
                        .HasColumnName("can_edit");

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("category_name");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<int>("Number")
                        .HasColumnType("integer")
                        .HasColumnName("number");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_categorys");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_categorys_company_id");

                    b.HasIndex("Number")
                        .IsUnique()
                        .HasDatabaseName("ix_categorys_number");

                    b.ToTable("categorys", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-************"),
                            CanEdit = false,
                            CategoryName = "Advertising",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 1,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-************"),
                            CanEdit = false,
                            CategoryName = "Car and Truck Expenses",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 2,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-************"),
                            CanEdit = false,
                            CategoryName = "Contractors",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 3,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000004"),
                            CanEdit = false,
                            CategoryName = "Education and Training",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 4,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000005"),
                            CanEdit = false,
                            CategoryName = "Employee Benefits",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 5,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000006"),
                            CanEdit = false,
                            CategoryName = "Meals and Entertainment",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 6,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000007"),
                            CanEdit = false,
                            CategoryName = "Office Expenses and Postage",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 7,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-************"),
                            CanEdit = false,
                            CategoryName = "Other Expenses",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 8,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000009"),
                            CanEdit = false,
                            CategoryName = "Personal",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 9,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-************"),
                            CanEdit = false,
                            CategoryName = "Professional Services",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 10,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000011"),
                            CanEdit = false,
                            CategoryName = "Rent or Lease",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 11,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000012"),
                            CanEdit = false,
                            CategoryName = "Supplies",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 12,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000013"),
                            CanEdit = false,
                            CategoryName = "Travel",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 13,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000014"),
                            CanEdit = false,
                            CategoryName = "Uncategorized Expenses",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 14,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000015"),
                            CanEdit = false,
                            CategoryName = "Utilities",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 15,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("********-0000-0000-0000-000000000016"),
                            CanEdit = false,
                            CategoryName = "Cost of Goods Sold",
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Number = 16,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("InnoBook.Entities.CategoryItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("boolean")
                        .HasColumnName("can_edit");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("ItemName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("item_name");

                    b.Property<int>("Number")
                        .HasColumnType("integer")
                        .HasColumnName("number");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_category_item");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_category_item_category_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_category_item_company_id");

                    b.HasIndex("Number")
                        .IsUnique()
                        .HasDatabaseName("ix_category_item_number");

                    b.ToTable("category_item", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "None",
                            Number = 1,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Gas",
                            Number = 2,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Mileage",
                            Number = 3,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000004"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Repairs",
                            Number = 4,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000005"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Vehicle Insurance",
                            Number = 5,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000006"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Vehicle Licensing",
                            Number = 6,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000007"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "None",
                            Number = 7,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000004"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "None",
                            Number = 8,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000009"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000005"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Accident Insurance",
                            Number = 9,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000005"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Health Insurance",
                            Number = 10,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000011"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000005"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Life Insurance",
                            Number = 11,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000012"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000006"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Entertainment",
                            Number = 12,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000013"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000006"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Restaurants/Dining",
                            Number = 13,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000014"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Hardware",
                            Number = 14,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000015"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Office Supplies",
                            Number = 15,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000016"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Packaging",
                            Number = 16,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000017"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Postage",
                            Number = 17,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000018"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Printing",
                            Number = 18,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000019"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Shipping and Couriers",
                            Number = 19,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000020"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Software",
                            Number = 20,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000021"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000007"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Stationery",
                            Number = 21,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Bank Fees",
                            Number = 22,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Business Insurance",
                            Number = 23,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Commissions",
                            Number = 24,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000025"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Depreciation",
                            Number = 25,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000026"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Interest - Mortgage",
                            Number = 26,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000027"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Interest - Other",
                            Number = 27,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000028"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Online Services",
                            Number = 28,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000029"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Reference Materials",
                            Number = 29,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000030"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Repairs and Maintenance",
                            Number = 30,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000031"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Subscriptions/Dues/Memberships",
                            Number = 31,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000032"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Taxes and Licenses",
                            Number = 32,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000033"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Wages",
                            Number = 33,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000034"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000009"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "None",
                            Number = 34,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Accounting",
                            Number = 35,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-************"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Legal Fees",
                            Number = 36,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000011"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Equipment",
                            Number = 37,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000038"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000011"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Machinery",
                            Number = 38,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000039"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000011"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Office Space",
                            Number = 39,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000040"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000011"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Vehicles",
                            Number = 40,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000041"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000012"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "None",
                            Number = 41,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000042"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000013"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Airfare",
                            Number = 42,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000043"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000013"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Hotel/Lodging/Accommodation",
                            Number = 43,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000044"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000013"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Taxi and Parking",
                            Number = 44,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000045"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000014"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "None",
                            Number = 45,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000046"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000015"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Gas and Electrical",
                            Number = 46,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000047"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000015"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Phone",
                            Number = 47,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000048"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000016"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Cost of Billed Expenses",
                            Number = 48,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-000000000049"),
                            CanEdit = false,
                            CategoryId = new Guid("********-0000-0000-0000-000000000016"),
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            ItemName = "Cost of Shipping and Handling",
                            Number = 49,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("InnoBook.Entities.Client", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<string>("AddressLine1")
                        .HasColumnType("text")
                        .HasColumnName("address_line1");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("text")
                        .HasColumnName("address_line2");

                    b.Property<string>("BusinessPhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("business_phone_number");

                    b.Property<string>("ClientName")
                        .HasColumnType("text")
                        .HasColumnName("client_name");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<string>("Country")
                        .HasColumnType("text")
                        .HasColumnName("country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text")
                        .HasColumnName("email_address");

                    b.Property<string>("FirstName")
                        .HasColumnType("text")
                        .HasColumnName("first_name");

                    b.Property<bool>("IsInternal")
                        .HasColumnType("boolean")
                        .HasColumnName("is_internal");

                    b.Property<string>("LastName")
                        .HasColumnType("text")
                        .HasColumnName("last_name");

                    b.Property<string>("MobilePhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("mobile_phone_number");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text")
                        .HasColumnName("postal_code");

                    b.Property<string>("PosteBusinessPhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("poste_business_phone_number");

                    b.Property<string>("PosteMobilePhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("poste_mobile_phone_number");

                    b.Property<string>("PostePhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("poste_phone_number");

                    b.Property<string>("StateProvince")
                        .HasColumnType("text")
                        .HasColumnName("state_province");

                    b.Property<string>("TownCity")
                        .HasColumnType("text")
                        .HasColumnName("town_city");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.HasKey("Id")
                        .HasName("pk_clients");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_clients_company_id");

                    b.ToTable("clients", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<string>("Adress")
                        .HasColumnType("text")
                        .HasColumnName("adress");

                    b.Property<string>("Adress2")
                        .HasColumnType("text")
                        .HasColumnName("adress2");

                    b.Property<string>("BusinessName")
                        .HasColumnType("text")
                        .HasColumnName("business_name");

                    b.Property<string>("City")
                        .HasColumnType("text")
                        .HasColumnName("city");

                    b.Property<string>("CompanyImage")
                        .HasColumnType("text")
                        .HasColumnName("company_image");

                    b.Property<string>("Country")
                        .HasColumnType("text")
                        .HasColumnName("country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Currency")
                        .HasColumnType("text")
                        .HasColumnName("currency");

                    b.Property<string>("DateFormat")
                        .HasColumnType("text")
                        .HasColumnName("date_format");

                    b.Property<string>("Email")
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<int?>("FiscalDay")
                        .HasColumnType("integer")
                        .HasColumnName("fiscal_day");

                    b.Property<string>("FiscalMonth")
                        .HasColumnType("text")
                        .HasColumnName("fiscal_month");

                    b.Property<bool>("IsPremiumCompany")
                        .HasColumnType("boolean")
                        .HasColumnName("is_premium_company");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<string>("Phone")
                        .HasColumnType("text")
                        .HasColumnName("phone");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text")
                        .HasColumnName("postal_code");

                    b.Property<string>("Province")
                        .HasColumnType("text")
                        .HasColumnName("province");

                    b.Property<decimal?>("Rate")
                        .HasColumnType("numeric")
                        .HasColumnName("rate");

                    b.Property<string>("StartWeekOn")
                        .HasColumnType("text")
                        .HasColumnName("start_week_on");

                    b.Property<string>("StripeCustomerId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("stripe_customer_id");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text")
                        .HasColumnName("time_zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_companies");

                    b.ToTable("companies", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.CompanyPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<int>("AdditionalUsers")
                        .HasColumnType("integer")
                        .HasColumnName("additional_users");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uuid")
                        .HasColumnName("plan_id");

                    b.Property<int>("RenewType")
                        .HasColumnType("integer")
                        .HasColumnName("renew_type");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("SubscriptionId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("subscription_id");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("numeric")
                        .HasColumnName("total_price");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_company_plans");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_company_plans_company_id");

                    b.HasIndex("PlanId")
                        .HasDatabaseName("ix_company_plans_plan_id");

                    b.ToTable("company_plans", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.CompanyTax", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("numeric")
                        .HasColumnName("amount");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("TaxeNumber")
                        .HasColumnType("text")
                        .HasColumnName("taxe_number");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_company_tax");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_company_tax_company_id");

                    b.ToTable("company_tax", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Expenses", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<Guid>("CategoryItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_item_id");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date");

                    b.Property<DateTime?>("DatePaid")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_paid");

                    b.Property<string>("ExpensesName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("expenses_name");

                    b.Property<string>("Img")
                        .HasColumnType("text")
                        .HasColumnName("img");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid")
                        .HasColumnName("merchant_id");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric")
                        .HasColumnName("paid_amount");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("isArchive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_archive");

                    b.HasKey("Id")
                        .HasName("pk_expenses");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_expenses_category_id");

                    b.HasIndex("CategoryItemId")
                        .HasDatabaseName("ix_expenses_category_item_id");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_expenses_client_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_expenses_company_id");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_expenses_merchant_id");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("ix_expenses_project_id");

                    b.ToTable("expenses", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<Guid?>("ContractorId")
                        .HasColumnType("uuid")
                        .HasColumnName("contractor_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DatePaid")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_paid");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("due_date");

                    b.Property<string>("Img")
                        .HasColumnType("text")
                        .HasColumnName("img");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("invoice_date");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("text")
                        .HasColumnName("invoice_number");

                    b.Property<bool>("IsEstimate")
                        .HasColumnType("boolean")
                        .HasColumnName("is_estimate");

                    b.Property<Guid?>("ItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("item_id");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric")
                        .HasColumnName("paid_amount");

                    b.Property<int>("Position")
                        .HasColumnType("integer")
                        .HasColumnName("position");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<decimal>("Rate")
                        .HasColumnType("numeric")
                        .HasColumnName("rate");

                    b.Property<string>("Reference")
                        .HasColumnType("text")
                        .HasColumnName("reference");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("numeric")
                        .HasColumnName("tax_amount");

                    b.Property<decimal>("TimeAmount")
                        .HasColumnType("numeric")
                        .HasColumnName("time_amount");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric")
                        .HasColumnName("total_amount");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("isArchive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_archive");

                    b.Property<bool>("isPaid")
                        .HasColumnType("boolean")
                        .HasColumnName("is_paid");

                    b.HasKey("Id")
                        .HasName("pk_invoices");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_invoices_client_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_invoices_company_id");

                    b.HasIndex("ItemId")
                        .HasDatabaseName("ix_invoices_item_id");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("ix_invoices_project_id");

                    b.ToTable("invoices", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.InvoiceSend", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Email")
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<bool>("IsEstimate")
                        .HasColumnType("boolean")
                        .HasColumnName("is_estimate");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_invoice_send");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_invoice_send_client_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_invoice_send_company_id");

                    b.HasIndex("InvoiceId")
                        .HasDatabaseName("ix_invoice_send_invoice_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_invoice_send_user_id");

                    b.ToTable("invoice_send", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Item", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("ItemName")
                        .HasColumnType("text")
                        .HasColumnName("item_name");

                    b.Property<decimal?>("Rate")
                        .HasColumnType("numeric")
                        .HasColumnName("rate");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("isArchive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_archive");

                    b.HasKey("Id")
                        .HasName("pk_items");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_items_company_id");

                    b.ToTable("items", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.ItemExpense", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<Guid>("ExpensesId")
                        .HasColumnType("uuid")
                        .HasColumnName("expenses_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<string>("description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<decimal>("qty")
                        .HasColumnType("numeric")
                        .HasColumnName("qty");

                    b.Property<decimal>("rate")
                        .HasColumnType("numeric")
                        .HasColumnName("rate");

                    b.Property<decimal>("total")
                        .HasColumnType("numeric")
                        .HasColumnName("total");

                    b.HasKey("Id")
                        .HasName("pk_item_expense");

                    b.HasIndex("ExpensesId")
                        .HasDatabaseName("ix_item_expense_expenses_id");

                    b.ToTable("item_expense", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.ItemInvoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DateSelectItem")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_select_item");

                    b.Property<Guid?>("ExpensesId")
                        .HasColumnType("uuid")
                        .HasColumnName("expenses_id");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<Guid?>("ItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("item_id");

                    b.Property<int>("Position")
                        .HasColumnType("integer")
                        .HasColumnName("position");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("service_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<decimal?>("qty")
                        .HasColumnType("numeric")
                        .HasColumnName("qty");

                    b.Property<decimal?>("rate")
                        .HasColumnType("numeric")
                        .HasColumnName("rate");

                    b.Property<string>("trackingId")
                        .HasColumnType("text")
                        .HasColumnName("tracking_id");

                    b.HasKey("Id")
                        .HasName("pk_item_invoice");

                    b.HasIndex("ExpensesId")
                        .HasDatabaseName("ix_item_invoice_expenses_id");

                    b.HasIndex("InvoiceId")
                        .HasDatabaseName("ix_item_invoice_invoice_id");

                    b.HasIndex("ItemId")
                        .HasDatabaseName("ix_item_invoice_item_id");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("ix_item_invoice_project_id");

                    b.HasIndex("ServiceId")
                        .HasDatabaseName("ix_item_invoice_service_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_item_invoice_user_id");

                    b.ToTable("item_invoice", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Member", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("LeftAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("left_at");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_members");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_members_company_id");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("ix_members_project_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_members_user_id");

                    b.ToTable("members", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<Guid?>("ExpensesId")
                        .HasColumnType("uuid")
                        .HasColumnName("expenses_id");

                    b.Property<string>("MerchantName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("merchant_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_merchant");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_merchant_company_id");

                    b.HasIndex("ExpensesId")
                        .HasDatabaseName("ix_merchant_expenses_id");

                    b.ToTable("merchant", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("DatePayment")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_payment");

                    b.Property<string>("IdPaymentMethod")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("id_payment_method");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<bool>("NotificationSent")
                        .HasColumnType("boolean")
                        .HasColumnName("notification_sent");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric")
                        .HasColumnName("paid_amount");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_payment");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_payment_company_id");

                    b.HasIndex("InvoiceId")
                        .HasDatabaseName("ix_payment_invoice_id");

                    b.ToTable("payment", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<string>("Code")
                        .HasColumnType("text")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("PermissionName")
                        .HasColumnType("text")
                        .HasColumnName("permission_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_permissions");

                    b.ToTable("permissions", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Plan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<decimal>("AdditionalUserPrice")
                        .HasColumnType("numeric")
                        .HasColumnName("additional_user_price");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("HasAccountingUser")
                        .HasColumnType("boolean")
                        .HasColumnName("has_accounting_user");

                    b.Property<bool>("HasCalendar")
                        .HasColumnType("boolean")
                        .HasColumnName("has_calendar");

                    b.Property<bool>("HasContractingUser")
                        .HasColumnType("boolean")
                        .HasColumnName("has_contracting_user");

                    b.Property<bool>("HasDashboard")
                        .HasColumnType("boolean")
                        .HasColumnName("has_dashboard");

                    b.Property<bool>("HasEstimates")
                        .HasColumnType("boolean")
                        .HasColumnName("has_estimates");

                    b.Property<bool>("HasExpenses")
                        .HasColumnType("boolean")
                        .HasColumnName("has_expenses");

                    b.Property<bool>("HasIntegrations")
                        .HasColumnType("boolean")
                        .HasColumnName("has_integrations");

                    b.Property<bool>("HasReports")
                        .HasColumnType("boolean")
                        .HasColumnName("has_reports");

                    b.Property<bool>("HasTimeTracking")
                        .HasColumnType("boolean")
                        .HasColumnName("has_time_tracking");

                    b.Property<bool>("HasUnlimitedClients")
                        .HasColumnType("boolean")
                        .HasColumnName("has_unlimited_clients");

                    b.Property<bool>("HasUnlimitedInvoices")
                        .HasColumnType("boolean")
                        .HasColumnName("has_unlimited_invoices");

                    b.Property<bool>("HasUnlimitedUsers")
                        .HasColumnType("boolean")
                        .HasColumnName("has_unlimited_users");

                    b.Property<bool>("IsRecommended")
                        .HasColumnType("boolean")
                        .HasColumnName("is_recommended");

                    b.Property<int>("MaxClients")
                        .HasColumnType("integer")
                        .HasColumnName("max_clients");

                    b.Property<int>("MaxTeamMembers")
                        .HasColumnType("integer")
                        .HasColumnName("max_team_members");

                    b.Property<decimal>("MonthlyPrice")
                        .HasColumnType("numeric")
                        .HasColumnName("monthly_price");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<int>("PlanType")
                        .HasColumnType("integer")
                        .HasColumnName("plan_type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<decimal>("YearlyPrice")
                        .HasColumnType("numeric")
                        .HasColumnName("yearly_price");

                    b.HasKey("Id")
                        .HasName("pk_plans");

                    b.ToTable("plans", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            AdditionalUserPrice = 0m,
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Description = "Start your transactions journey with essential features and flexibility.",
                            HasAccountingUser = false,
                            HasCalendar = true,
                            HasContractingUser = false,
                            HasDashboard = true,
                            HasEstimates = false,
                            HasExpenses = false,
                            HasIntegrations = false,
                            HasReports = false,
                            HasTimeTracking = true,
                            HasUnlimitedClients = false,
                            HasUnlimitedInvoices = true,
                            HasUnlimitedUsers = true,
                            IsRecommended = false,
                            MaxClients = 3,
                            MaxTeamMembers = 1,
                            MonthlyPrice = 0m,
                            Name = "Departure Package",
                            PlanType = 0,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            YearlyPrice = 0m
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            AdditionalUserPrice = 3m,
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Description = "Unlock advanced tools and enhanced security for seamless transactions.",
                            HasAccountingUser = true,
                            HasCalendar = true,
                            HasContractingUser = true,
                            HasDashboard = true,
                            HasEstimates = true,
                            HasExpenses = true,
                            HasIntegrations = true,
                            HasReports = true,
                            HasTimeTracking = true,
                            HasUnlimitedClients = false,
                            HasUnlimitedInvoices = true,
                            HasUnlimitedUsers = false,
                            IsRecommended = true,
                            MaxClients = 10,
                            MaxTeamMembers = 3,
                            MonthlyPrice = 10m,
                            Name = "Professional Access",
                            PlanType = 1,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            YearlyPrice = 108m
                        },
                        new
                        {
                            Id = new Guid("*************-0000-0000-************"),
                            AdditionalUserPrice = 6m,
                            CreatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            CreatedBy = "",
                            Description = "Access premium features for optimal transaction control and performance.",
                            HasAccountingUser = true,
                            HasCalendar = true,
                            HasContractingUser = true,
                            HasDashboard = true,
                            HasEstimates = true,
                            HasExpenses = true,
                            HasIntegrations = true,
                            HasReports = true,
                            HasTimeTracking = true,
                            HasUnlimitedClients = true,
                            HasUnlimitedInvoices = true,
                            HasUnlimitedUsers = false,
                            IsRecommended = false,
                            MaxClients = 0,
                            MaxTeamMembers = 5,
                            MonthlyPrice = 20m,
                            Name = "Elite Plan",
                            PlanType = 2,
                            UpdatedAt = new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            YearlyPrice = 216m
                        });
                });

            modelBuilder.Entity("InnoBook.Entities.Project", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<decimal>("ActualHours")
                        .HasColumnType("numeric")
                        .HasColumnName("actual_hours");

                    b.Property<bool>("Billable")
                        .HasColumnType("boolean")
                        .HasColumnName("billable");

                    b.Property<decimal>("Budget")
                        .HasColumnType("numeric")
                        .HasColumnName("budget");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<decimal>("FlatRate")
                        .HasColumnType("numeric")
                        .HasColumnName("flat_rate");

                    b.Property<decimal?>("HourlyRate")
                        .HasColumnType("numeric")
                        .HasColumnName("hourly_rate");

                    b.Property<int>("Option")
                        .HasColumnType("integer")
                        .HasColumnName("option");

                    b.Property<string>("ProjectName")
                        .HasColumnType("text")
                        .HasColumnName("project_name");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<decimal>("TotalHours")
                        .HasColumnType("numeric")
                        .HasColumnName("total_hours");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("isArchive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_archive");

                    b.HasKey("Id")
                        .HasName("pk_projects");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_projects_client_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_projects_company_id");

                    b.ToTable("projects", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<string>("Code")
                        .HasColumnType("text")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("RoleName")
                        .HasColumnType("text")
                        .HasColumnName("role_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_roles");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.RolePermission", b =>
                {
                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid")
                        .HasColumnName("permission_id");

                    b.HasKey("RoleId", "PermissionId")
                        .HasName("pk_role_permissions");

                    b.HasIndex("PermissionId")
                        .HasDatabaseName("ix_role_permissions_permission_id");

                    b.ToTable("role_permissions", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Service", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<decimal?>("ActualHours")
                        .HasColumnType("numeric")
                        .HasColumnName("actual_hours");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<decimal?>("Rate")
                        .HasColumnType("numeric")
                        .HasColumnName("rate");

                    b.Property<string>("ServiceName")
                        .HasColumnType("text")
                        .HasColumnName("service_name");

                    b.Property<decimal?>("TotalHours")
                        .HasColumnType("numeric")
                        .HasColumnName("total_hours");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("isArchive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_archive");

                    b.HasKey("Id")
                        .HasName("pk_services");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_services_company_id");

                    b.ToTable("services", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Tax", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("CompanyTaxId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_tax_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<Guid?>("ExpensesId")
                        .HasColumnType("uuid")
                        .HasColumnName("expenses_id");

                    b.Property<Guid?>("ItemExpenseId")
                        .HasColumnType("uuid")
                        .HasColumnName("item_expense_id");

                    b.Property<Guid?>("ItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("item_id");

                    b.Property<Guid?>("ItemInvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("item_invoice_id");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("service_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_taxs");

                    b.HasIndex("CompanyTaxId")
                        .HasDatabaseName("ix_taxs_company_tax_id");

                    b.HasIndex("ExpensesId")
                        .HasDatabaseName("ix_taxs_expenses_id");

                    b.HasIndex("ItemExpenseId")
                        .HasDatabaseName("ix_taxs_item_expense_id");

                    b.HasIndex("ItemId")
                        .HasDatabaseName("ix_taxs_item_id");

                    b.HasIndex("ItemInvoiceId")
                        .HasDatabaseName("ix_taxs_item_invoice_id");

                    b.HasIndex("ServiceId")
                        .HasDatabaseName("ix_taxs_service_id");

                    b.ToTable("taxs", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.TimeTracking", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<bool>("Billable")
                        .HasColumnType("boolean")
                        .HasColumnName("billable");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("EndTime")
                        .HasColumnType("text")
                        .HasColumnName("end_time");

                    b.Property<Guid?>("MemberId")
                        .HasColumnType("uuid")
                        .HasColumnName("member_id");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("service_id");

                    b.Property<string>("StartTime")
                        .HasColumnType("text")
                        .HasColumnName("start_time");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<bool>("isBilled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_billed");

                    b.HasKey("Id")
                        .HasName("pk_time_trackings");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("ix_time_trackings_client_id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_time_trackings_company_id");

                    b.HasIndex("MemberId")
                        .HasDatabaseName("ix_time_trackings_member_id");

                    b.HasIndex("ProjectId")
                        .HasDatabaseName("ix_time_trackings_project_id");

                    b.HasIndex("ServiceId")
                        .HasDatabaseName("ix_time_trackings_service_id");

                    b.ToTable("time_trackings", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Email")
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<DateTime?>("ExpireTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expire_time");

                    b.Property<string>("FirstName")
                        .HasColumnType("text")
                        .HasColumnName("first_name");

                    b.Property<bool?>("Is2FAEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is2fa_enabled");

                    b.Property<bool?>("IsRunning")
                        .HasColumnType("boolean")
                        .HasColumnName("is_running");

                    b.Property<DateTime?>("LastLoginTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login_time");

                    b.Property<string>("LastName")
                        .HasColumnType("text")
                        .HasColumnName("last_name");

                    b.Property<string>("Password")
                        .HasColumnType("text")
                        .HasColumnName("password");

                    b.Property<string>("PasswordResetToken")
                        .HasColumnType("text")
                        .HasColumnName("password_reset_token");

                    b.Property<string>("Role")
                        .HasColumnType("text")
                        .HasColumnName("role");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("TimeZoneId")
                        .HasColumnType("text")
                        .HasColumnName("time_zone_id");

                    b.Property<string>("Timer")
                        .HasColumnType("text")
                        .HasColumnName("timer");

                    b.Property<DateTime?>("TimerStartTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("timer_start_time");

                    b.Property<DateTime?>("TokenExpiration")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("token_expiration");

                    b.Property<string>("TwoFactorCode")
                        .HasColumnType("text")
                        .HasColumnName("two_factor_code");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<string>("Username")
                        .HasColumnType("text")
                        .HasColumnName("username");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_users_email");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("ix_users_username");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.UserBusiness", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<string>("Role")
                        .HasColumnType("text")
                        .HasColumnName("role");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_businesses");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_user_businesses_company_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_user_businesses_user_id");

                    b.ToTable("user_businesses", (string)null);
                });

            modelBuilder.Entity("ProjectService", b =>
                {
                    b.Property<Guid>("ProjectsId")
                        .HasColumnType("uuid")
                        .HasColumnName("projects_id");

                    b.Property<Guid>("ServicesId")
                        .HasColumnType("uuid")
                        .HasColumnName("services_id");

                    b.HasKey("ProjectsId", "ServicesId")
                        .HasName("pk_project_service");

                    b.HasIndex("ServicesId")
                        .HasDatabaseName("ix_project_service_services_id");

                    b.ToTable("projectService", (string)null);
                });

            modelBuilder.Entity("InnoBook.Entities.Attachment", b =>
                {
                    b.HasOne("InnoBook.Entities.Expenses", "Expenses")
                        .WithMany("Attachments")
                        .HasForeignKey("ExpensesId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_attachment_expenses_expenses_id");

                    b.HasOne("InnoBook.Entities.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .HasConstraintName("fk_attachment_invoices_invoice_id");

                    b.Navigation("Expenses");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("InnoBook.Entities.Category", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Categorys")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_categorys_companies_company_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.CategoryItem", b =>
                {
                    b.HasOne("InnoBook.Entities.Category", "Category")
                        .WithMany("CategoryItems")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_category_item_categorys_category_id");

                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("CategoryItems")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_category_item_companies_company_id");

                    b.Navigation("Category");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.Client", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Clients")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_clients_companies_company_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.CompanyPlan", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_company_plans_companies_company_id");

                    b.HasOne("InnoBook.Entities.Plan", "Plan")
                        .WithMany("CompanyPlans")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_company_plans_plans_plan_id");

                    b.Navigation("Company");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("InnoBook.Entities.CompanyTax", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("CompanyTaxs")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_company_tax_companies_company_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.Expenses", b =>
                {
                    b.HasOne("InnoBook.Entities.Category", "Category")
                        .WithMany("Expenses")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_expenses_categorys_category_id");

                    b.HasOne("InnoBook.Entities.CategoryItem", "CategoryItem")
                        .WithMany("Expenses")
                        .HasForeignKey("CategoryItemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_expenses_category_item_category_item_id");

                    b.HasOne("InnoBook.Entities.Client", "Client")
                        .WithMany("Expenses")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_expenses_clients_client_id");

                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Expenses")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_expenses_companies_company_id");

                    b.HasOne("InnoBook.Entities.Merchant", "Merchant")
                        .WithMany("Expenses")
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_expenses_merchant_merchant_id");

                    b.HasOne("InnoBook.Entities.Project", "Project")
                        .WithMany("Expenses")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_expenses_projects_project_id");

                    b.Navigation("Category");

                    b.Navigation("CategoryItem");

                    b.Navigation("Client");

                    b.Navigation("Company");

                    b.Navigation("Merchant");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("InnoBook.Entities.Invoice", b =>
                {
                    b.HasOne("InnoBook.Entities.Client", "Client")
                        .WithMany("Invoices")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_invoices_clients_client_id");

                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Invoices")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_invoices_companies_company_id");

                    b.HasOne("InnoBook.Entities.Item", "Item")
                        .WithMany()
                        .HasForeignKey("ItemId")
                        .HasConstraintName("fk_invoices_items_item_id");

                    b.HasOne("InnoBook.Entities.Project", "Project")
                        .WithMany("Invoices")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_invoices_projects_project_id");

                    b.Navigation("Client");

                    b.Navigation("Company");

                    b.Navigation("Item");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("InnoBook.Entities.InvoiceSend", b =>
                {
                    b.HasOne("InnoBook.Entities.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_invoice_send_clients_client_id");

                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_invoice_send_companies_company_id");

                    b.HasOne("InnoBook.Entities.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_invoice_send_invoices_invoice_id");

                    b.HasOne("InnoBook.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_invoice_send_users_user_id");

                    b.Navigation("Client");

                    b.Navigation("Company");

                    b.Navigation("Invoice");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InnoBook.Entities.Item", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_items_companies_company_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.ItemExpense", b =>
                {
                    b.HasOne("InnoBook.Entities.Expenses", "Expenses")
                        .WithMany("ItemExpense")
                        .HasForeignKey("ExpensesId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_item_expense_expenses_expenses_id");

                    b.Navigation("Expenses");
                });

            modelBuilder.Entity("InnoBook.Entities.ItemInvoice", b =>
                {
                    b.HasOne("InnoBook.Entities.Expenses", "Expenses")
                        .WithMany()
                        .HasForeignKey("ExpensesId")
                        .HasConstraintName("fk_item_invoice_expenses_expenses_id");

                    b.HasOne("InnoBook.Entities.Invoice", "Invoice")
                        .WithMany("ItemInvoices")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_item_invoice_invoices_invoice_id");

                    b.HasOne("InnoBook.Entities.Item", "Item")
                        .WithMany()
                        .HasForeignKey("ItemId")
                        .HasConstraintName("fk_item_invoice_items_item_id");

                    b.HasOne("InnoBook.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .HasConstraintName("fk_item_invoice_projects_project_id");

                    b.HasOne("InnoBook.Entities.Service", "Service")
                        .WithMany()
                        .HasForeignKey("ServiceId")
                        .HasConstraintName("fk_item_invoice_services_service_id");

                    b.HasOne("InnoBook.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_item_invoice_users_user_id");

                    b.Navigation("Expenses");

                    b.Navigation("Invoice");

                    b.Navigation("Item");

                    b.Navigation("Project");

                    b.Navigation("Service");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InnoBook.Entities.Member", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Members")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_members_companies_company_id");

                    b.HasOne("InnoBook.Entities.Project", "Project")
                        .WithMany("Members")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_members_projects_project_id");

                    b.HasOne("InnoBook.Entities.User", "User")
                        .WithMany("Members")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_members_users_user_id");

                    b.Navigation("Company");

                    b.Navigation("Project");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InnoBook.Entities.Merchant", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Merchants")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_companies_company_id");

                    b.HasOne("InnoBook.Entities.Expenses", null)
                        .WithMany("Merchants")
                        .HasForeignKey("ExpensesId")
                        .HasConstraintName("fk_merchant_expenses_expenses_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.Payment", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Payments")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_payment_companies_company_id");

                    b.HasOne("InnoBook.Entities.Invoice", "Invoice")
                        .WithMany("Payments")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_payment_invoices_invoice_id");

                    b.Navigation("Company");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("InnoBook.Entities.Project", b =>
                {
                    b.HasOne("InnoBook.Entities.Client", "Client")
                        .WithMany("Projects")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_projects_clients_client_id");

                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("Projects")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_projects_companies_company_id");

                    b.Navigation("Client");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.RolePermission", b =>
                {
                    b.HasOne("InnoBook.Entities.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_permissions_permissions_permission_id");

                    b.HasOne("InnoBook.Entities.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_permissions_roles_role_id");

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("InnoBook.Entities.Service", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("fk_services_companies_company_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("InnoBook.Entities.Tax", b =>
                {
                    b.HasOne("InnoBook.Entities.CompanyTax", "CompanyTax")
                        .WithMany("Taxes")
                        .HasForeignKey("CompanyTaxId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_taxs_company_tax_company_tax_id");

                    b.HasOne("InnoBook.Entities.Expenses", "Expenses")
                        .WithMany()
                        .HasForeignKey("ExpensesId")
                        .HasConstraintName("fk_taxs_expenses_expenses_id");

                    b.HasOne("InnoBook.Entities.ItemExpense", "ItemExpense")
                        .WithMany("Taxes")
                        .HasForeignKey("ItemExpenseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_taxs_item_expense_item_expense_id");

                    b.HasOne("InnoBook.Entities.Item", "Item")
                        .WithMany("Taxes")
                        .HasForeignKey("ItemId")
                        .HasConstraintName("fk_taxs_items_item_id");

                    b.HasOne("InnoBook.Entities.ItemInvoice", "ItemInvoice")
                        .WithMany("Taxes")
                        .HasForeignKey("ItemInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_taxs_item_invoice_item_invoice_id");

                    b.HasOne("InnoBook.Entities.Service", "Service")
                        .WithMany("Taxes")
                        .HasForeignKey("ServiceId")
                        .HasConstraintName("fk_taxs_services_service_id");

                    b.Navigation("CompanyTax");

                    b.Navigation("Expenses");

                    b.Navigation("Item");

                    b.Navigation("ItemExpense");

                    b.Navigation("ItemInvoice");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("InnoBook.Entities.TimeTracking", b =>
                {
                    b.HasOne("InnoBook.Entities.Client", "Client")
                        .WithMany("TimeTrackings")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_time_trackings_clients_client_id");

                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_time_trackings_companies_company_id");

                    b.HasOne("InnoBook.Entities.Member", "Member")
                        .WithMany("TimeTrackings")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_time_trackings_members_member_id");

                    b.HasOne("InnoBook.Entities.Project", "Project")
                        .WithMany("TimeTrackings")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_time_trackings_projects_project_id");

                    b.HasOne("InnoBook.Entities.Service", "Service")
                        .WithMany("TimeTrackings")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_time_trackings_services_service_id");

                    b.Navigation("Client");

                    b.Navigation("Company");

                    b.Navigation("Member");

                    b.Navigation("Project");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("InnoBook.Entities.UserBusiness", b =>
                {
                    b.HasOne("InnoBook.Entities.Company", "Company")
                        .WithMany("UserBusinesses")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_user_businesses_companies_company_id");

                    b.HasOne("InnoBook.Entities.User", "User")
                        .WithMany("UserBusinesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_user_businesses_users_user_id");

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ProjectService", b =>
                {
                    b.HasOne("InnoBook.Entities.Project", null)
                        .WithMany()
                        .HasForeignKey("ProjectsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_project_service_projects_projects_id");

                    b.HasOne("InnoBook.Entities.Service", null)
                        .WithMany()
                        .HasForeignKey("ServicesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_project_service_services_services_id");
                });

            modelBuilder.Entity("InnoBook.Entities.Category", b =>
                {
                    b.Navigation("CategoryItems");

                    b.Navigation("Expenses");
                });

            modelBuilder.Entity("InnoBook.Entities.CategoryItem", b =>
                {
                    b.Navigation("Expenses");
                });

            modelBuilder.Entity("InnoBook.Entities.Client", b =>
                {
                    b.Navigation("Expenses");

                    b.Navigation("Invoices");

                    b.Navigation("Projects");

                    b.Navigation("TimeTrackings");
                });

            modelBuilder.Entity("InnoBook.Entities.Company", b =>
                {
                    b.Navigation("CategoryItems");

                    b.Navigation("Categorys");

                    b.Navigation("Clients");

                    b.Navigation("CompanyTaxs");

                    b.Navigation("Expenses");

                    b.Navigation("Invoices");

                    b.Navigation("Members");

                    b.Navigation("Merchants");

                    b.Navigation("Payments");

                    b.Navigation("Projects");

                    b.Navigation("UserBusinesses");
                });

            modelBuilder.Entity("InnoBook.Entities.CompanyTax", b =>
                {
                    b.Navigation("Taxes");
                });

            modelBuilder.Entity("InnoBook.Entities.Expenses", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("ItemExpense");

                    b.Navigation("Merchants");
                });

            modelBuilder.Entity("InnoBook.Entities.Invoice", b =>
                {
                    b.Navigation("ItemInvoices");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("InnoBook.Entities.Item", b =>
                {
                    b.Navigation("Taxes");
                });

            modelBuilder.Entity("InnoBook.Entities.ItemExpense", b =>
                {
                    b.Navigation("Taxes");
                });

            modelBuilder.Entity("InnoBook.Entities.ItemInvoice", b =>
                {
                    b.Navigation("Taxes");
                });

            modelBuilder.Entity("InnoBook.Entities.Member", b =>
                {
                    b.Navigation("TimeTrackings");
                });

            modelBuilder.Entity("InnoBook.Entities.Merchant", b =>
                {
                    b.Navigation("Expenses");
                });

            modelBuilder.Entity("InnoBook.Entities.Plan", b =>
                {
                    b.Navigation("CompanyPlans");
                });

            modelBuilder.Entity("InnoBook.Entities.Project", b =>
                {
                    b.Navigation("Expenses");

                    b.Navigation("Invoices");

                    b.Navigation("Members");

                    b.Navigation("TimeTrackings");
                });

            modelBuilder.Entity("InnoBook.Entities.Role", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("InnoBook.Entities.Service", b =>
                {
                    b.Navigation("Taxes");

                    b.Navigation("TimeTrackings");
                });

            modelBuilder.Entity("InnoBook.Entities.User", b =>
                {
                    b.Navigation("Members");

                    b.Navigation("UserBusinesses");
                });
#pragma warning restore 612, 618
        }
    }
}

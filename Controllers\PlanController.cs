using InnoBook.Attributes;
using InnoBook.DTO.Plan;
using InnoBook.Entities;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class PlanController : BaseController
    {
        private readonly IPlanService _planService;

        public PlanController(IPlanService planService, IHttpContextAccessor httpContextAccessor)
            : base(httpContextAccessor)
        {
            _planService = planService;
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager)]
        [HttpGet("GetAllPlans")]
        public async Task<ActionResult<List<PlanDto>>> GetAllPlans()
        {
            try
            {
                var plans = await _planService.GetAllPlans();
                return Ok(plans);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager)]
        [HttpGet("GetCurrentPlan")]
        public async Task<ActionResult<CompanyPlan>> GetCurrentPlan()
        {
            try
            {
                var companyId = Guid.Parse(IdCompany);
                var plan = await _planService.GetCompanyCurrentPlan(companyId);
                return Ok(plan);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager)]
        [HttpGet("CalculatePlanPrice")]
        public async Task<ActionResult<decimal>> CalculatePlanPrice([FromQuery] Guid planId, [FromQuery] string billingInterval, [FromQuery] int additionalUsers)
        {
            try
            {
                var price = await _planService.CalculatePlanPrice(planId, billingInterval, additionalUsers);
                return Ok(new { price });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}

namespace InnoBook.DTO.Stripe
{
    public class StripeSessionRequest
    {
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public string Description { get; set; } = "Payment";

        /// <summary>
        /// Payment type: "one_time" or "subscription"
        /// </summary>
        public string PaymentType { get; set; } = "one_time";

        /// <summary>
        /// Billing interval for subscriptions: "month" or "year"
        /// </summary>
        public string BillingInterval { get; set; } = "month";

        /// <summary>
        /// Plan ID for subscription payments
        /// </summary>
        public string PlanId { get; set; }

        /// <summary>
        /// Number of additional users for the plan
        /// </summary>
        public int AdditionalUsers { get; set; } = 0;
    }
}

using InnoBook.DTO.Plan;
using InnoBook.Entities;

namespace InnoBook.Services.Interface
{
    public interface IPlanService
    {
        Task<List<PlanDto>> GetAllPlans();
        Task<CompanyPlanDto> GetCompanyCurrentPlan(Guid companyId);
        Task<CompanyPlanDto> SubscribeToPlan(Guid companyId, Guid planId, string billingInterval, int additionalUsers, string subId);
        Task<decimal> CalculatePlanPrice(Guid planId, string billingInterval, int additionalUsers);
    }
}

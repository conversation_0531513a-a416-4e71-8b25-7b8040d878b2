import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { GetClientQueryParam } from 'app/dto/interface/queryParameter.interface';
import { Client } from 'app/dto/interface/client.interface';
import { TranslateService } from '@ngx-translate/core';
import { ClientService } from './../../../service/client.service';
import { AddClientsDialog } from './../../../service/dialog/add-clients.dialog';
import { Component, DestroyRef, EventEmitter, inject, OnDestroy, OnInit, Output } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AvatarModule } from 'ngx-avatars';
import { debounceTime, Subject, Subscription } from 'rxjs';

@Component({
  selector: 'app-pos-client',
  standalone: true,
  imports: [SharedModule, AvatarModule, InnoInputSearchComponent, InnoSpinomponent],
  templateUrl: './pos-client.component.html',
  styleUrl: './pos-client.component.scss'
})
export class PosClientComponent implements OnInit, OnDestroy {
  @Output() ClientId = new EventEmitter(null)
  public selectIndex: number
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 50
  search: string = "";
  isLoading: boolean = false
  listClient: Client[] = []
  private _subscriptions: Subscription[] = [];
  private searchSubject = new Subject<string>();

  private destroyRef = inject(DestroyRef)
  private clientService = inject(ClientService)
  private addClientsDialog = inject(AddClientsDialog)
  private translate = inject(TranslateService);
  ngOnInit(): void {
    this.GetAllClient();
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      this.search = search ?? "";
      this.GetAllClient();
    });
    this._subscriptions.push(sb)
  }



  handleEditClient(item: any) {

    const dialogRef = this.addClientsDialog.open(item.id);;
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
        }
      })
    });
  }
  ChooseClient(Id: string, index: number) {
    this.selectIndex = index;
    this.ClientId.emit(Id);

  }
  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  NewClient() {
    const dialogRef = this.addClientsDialog.open(null);
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) this.GetAllClient()
      })
    });
  };
  GetAllClient() {
    this.isLoading = true;
    let query: GetClientQueryParam = {
      Page: this.currentPage,
      PageSize: this.pageSizesDefault,
      Search: this.search,

    }

    this.clientService.GetAllClient(query).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.isLoading = false;
        this.listClient = res.data

      }
    }
    )

  }
  ngOnDestroy(): void {
    if (this._subscriptions) {
      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}

﻿using InnoBook.Enum;

namespace InnoBook.DTO.Plan
{
    public class PlanDto
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal MonthlyPrice { get; set; }
        public decimal YearlyPrice { get; set; }
        public decimal AdditionalUserPrice { get; set; }
        public bool IsRecommended { get; set; }
        public int MaxClients { get; set; }
        public int MaxTeamMembers { get; set; }
        public bool HasUnlimitedInvoices { get; set; }
        public bool HasTimeTracking { get; set; }
        public bool HasCalendar { get; set; }
        public bool HasDashboard { get; set; }
        public bool HasExpenses { get; set; }
        public bool HasEstimates { get; set; }
        public bool HasIntegrations { get; set; }
        public bool HasReports { get; set; }
        public bool HasAccountingUser { get; set; }
        public bool HasContractingUser { get; set; }
        public bool HasUnlimitedUsers { get; set; }
        public bool HasUnlimitedClients { get; set; }
        public PlanEnum PlanType { get; set; }

        public PlanDto(Entities.Plan entity)
        {
            if (entity == null) return;
            Id = entity.Id.ToString();
            Name = entity.Name;
            Description = entity.Description;
            MonthlyPrice = entity.MonthlyPrice;
            YearlyPrice = entity.YearlyPrice;
            AdditionalUserPrice = entity.AdditionalUserPrice;
            IsRecommended = entity.IsRecommended;
            MaxClients = entity.MaxClients;
            MaxTeamMembers = entity.MaxTeamMembers;
            HasUnlimitedInvoices = entity.HasUnlimitedInvoices;
            HasTimeTracking = entity.HasTimeTracking;
            HasCalendar = entity.HasCalendar;
            HasDashboard = entity.HasDashboard;
            HasExpenses = entity.HasExpenses;
            HasEstimates = entity.HasEstimates;
            HasIntegrations = entity.HasIntegrations;
            HasReports = entity.HasReports;
            HasAccountingUser = entity.HasAccountingUser;
            HasContractingUser = entity.HasContractingUser;
            HasUnlimitedUsers = entity.HasUnlimitedUsers;
            HasUnlimitedClients = entity.HasUnlimitedClients;
            PlanType = entity.PlanType;
        }
    }
}

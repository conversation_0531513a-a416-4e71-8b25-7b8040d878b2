﻿using InnoBook.Attributes;
using InnoBook.Common;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ImagesController(IConfiguration config, HttpClient _httpClient, IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetFile")]
        public async Task<ActionResult> GetFile(string nameFile)
        {
            DigitalOcean digitalOcean = new DigitalOcean();
            var (fileStream, contentType, name) = await digitalOcean.GetFileMinio(config, IdCompany, nameFile);
            return File(fileStream, contentType, name);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetFileURL")]
        public async Task<IActionResult> GetFileURL(string nameFile)
            {
            DigitalOcean digitalOcean = new DigitalOcean();
            string url = digitalOcean.GetFileURL(config, IdCompany, nameFile);
            if (string.IsNullOrWhiteSpace(url))
                return BadRequest("URL is required.");

            try
            {
                
                return Ok(url);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error: {ex.Message}");
            }
        }
    }
}

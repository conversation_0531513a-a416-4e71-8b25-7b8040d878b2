import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class CdnService {

  private http = inject(HttpClient)
  constructor() { }
  GetFile(nameFile: string): Observable<any> {
    return this.http.get<any>(UrlApi + `/Images/GetFile?nameFile=${nameFile}`, { responseType: 'blob' as 'json' });
  }
  GetFileURL(nameFile: string): Observable<any> {
    return this.http.get<any>(UrlApi + `/Images/GetFileURL?nameFile=${nameFile}`, { responseType: 'blob' as 'json' });
  }

}

// Styles for the plan selection component
.border-green-600 {
  border-color: #059669;
  border-width: 2px;
}

// Hover effect for plan cards
.plan-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

// Checkmark animation
@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.text-green-500 svg {
  animation: checkmark-appear 0.3s ease-out forwards;
}

﻿using InnoLogiciel.Server.Enum.User;
using System.ComponentModel.DataAnnotations.Schema;

namespace InnoBook.DTO.User
{
    public class UserUpdateDTO
    {
        public Guid? Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public bool? IsRunning { get; set; }
        public string? Timer { get; set; }
        public DateTime? TimerStartTime { get; set; }
        public string? Username { get; set; }
       
     
        // 2FA fields
        public bool? Is2FAEnabled { get; set; }
        public string? TwoFactorCode { get; set; }
        public DateTime? ExpireTime { get; set; }

        // Additional fields
        public UserStatus? Status { get; set; }
        public DateTime? LastLoginTime { get; set; }

        public string? PasswordResetToken { get; set; }
        public DateTime? TokenExpiration { get; set; }

        public string? TimeZoneId { get; set; }

    }
}

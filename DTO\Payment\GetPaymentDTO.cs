﻿namespace InnoBook.DTO.Payment
{
    public class GetPaymentDTO
    {
        public Guid Id { get; set; }
        public Guid InvoiceId { get; set; }
        public Guid CompanyId { get; set; }
        public string? Note { get; set; }
        public DateTime DatePayment { get; set; }
        public decimal PaidAmount { get; set; }
        public string IdPaymentMethod { get; set; }
        public bool NotificationSent { get; set; }
    }
}

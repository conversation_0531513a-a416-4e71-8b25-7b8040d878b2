using InnoBook.DTO.Stripe;
using Stripe.Checkout;

namespace InnoBook.Services.Interface
{
    public interface IStripeService
    {
        Task<Session> CreatePaymentSession(StripeSessionRequest request, string companyId, string userId);
        Task<Session> GetSession(string sessionId);
        Task<bool> VerifyWebhookSignature(string payload, string signature);
        Task ProcessWebhookEvent(string json, string signature);
    }
}

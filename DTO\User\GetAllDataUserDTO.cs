﻿using InnoLogiciel.Server.Enum.User;

namespace InnoBook.DTO.User
{
    public class GetAllDataUserDTO
    {
        public Guid Id { get; set; }
        public string? Password { get; set; }
        public UserStatus? Status { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        public string? Role { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Username { get; set; }
        public DateTime? ExpireTime { get; set; }
        public string? TwoFactorCode { get; set; }
        public string? TimeZoneId { get; set; }
    }
}

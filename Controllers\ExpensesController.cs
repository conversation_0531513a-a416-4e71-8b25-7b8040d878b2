using InnoBook.Attributes;
using InnoBook.Common;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Expenses;
using InnoBook.Request.Expenses;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class ExpensesController(IExpensesService expensesService, IConfiguration config, IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant, UserBusinessRole.Employee)]
        [HttpGet("GetAllExpenses")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllExpenses([FromQuery] GetExpenseRequestParam query)
        {
            query.Role = HttpContext.GetBusinessRole();
            var data = await expensesService.GetAllExpenses(query, IdCompany,IdUser);
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant, UserBusinessRole.Employee)]
        [HttpGet("GetExpensesById")]
        public async Task<IActionResult> GetExpensesById(string Id)
        {
            var result = await expensesService.GetExpensesById(Id);
            return Ok(result);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant, UserBusinessRole.Employee)]
        [HttpGet("GetAllUploadExpenses")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllUploadExpenses([FromQuery] GetUploadedAttachmentParam query)
        {
            query.Role = HttpContext.GetBusinessRole();
            query.CompanyId = IdCompany;
            query.UserId = IdUser;
            var data = await expensesService.GetAllUploadExpenses(query);
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant, UserBusinessRole.Employee)]
        [Ownership(typeof(RequestExpenses))]
        [HttpPost("UpdateExpenses")]
        public async Task<IActionResult> UpdateExpenses([FromBody] RequestExpenses expenses)
        {
            expenses.CompanyId = Guid.Parse(IdCompany);
            var result = await expensesService.UpdateExpenses(expenses,IdCompany, IdUser);
            return Ok(result);

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant, UserBusinessRole.Employee)]    
        [HttpPost("CreateExpenses")]
        public async Task<IActionResult> CreateExpenses(RequestExpenses expenses)
        {
            var result = await expensesService.CreateExpenses(expenses, IdUser, IdCompany);
            return Ok(result);

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpPost("DeleteExpenses")]
        public async Task<IActionResult> DeleteExpenses(List<Guid?> listExpenses)
        {
            var result = await expensesService.DeleteExpenses(listExpenses, IdUser);
            return Ok(result);

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPut("MarkAsPaid")]
        public async Task<IActionResult> MarkAsPaid(string Id)
        {
            var result = await expensesService.MarkAsPaid(Id);
            return Ok(result);

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant, UserBusinessRole.Employee)]
        [HttpPost("GetExpenseItemsByExpenseIds")]
        public async Task<IActionResult> GetExpenseItemsByExpenseIds([FromBody] List<string> expenseIds)
        {
            var result = await expensesService.GetExpenseItemsByExpenseIds(expenseIds);
            return Ok(result);
        }

    }
}

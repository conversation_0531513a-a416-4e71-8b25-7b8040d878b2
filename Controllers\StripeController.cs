using InnoBook.Attributes;
using InnoBook.DTO.Stripe;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Stripe;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class StripeController : BaseController
    {
        private readonly IStripeService _stripeService;

        public StripeController(IStripeService stripeService, IHttpContextAccessor httpContextAccessor)
            : base(httpContextAccessor)
        {
            _stripeService = stripeService;
        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpPost("CreateSession")]
        public async Task<ActionResult> CreateSession([FromBody] StripeSessionRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var session = await _stripeService.CreatePaymentSession(request, IdCompany, IdUser);
                return Ok(new { id = session.Id, url = session.Url });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("VerifyPayment")]
        public async Task<ActionResult> VerifyPayment([FromQuery] string sessionId)
        {
            if (string.IsNullOrEmpty(sessionId))
            {
                return BadRequest("Session ID is required");
            }

            try
            {
                var session = await _stripeService.GetSession(sessionId);
                return Ok(new
                {
                    id = session.Id,
                    status = session.Status,
                    amount = session.AmountTotal / 100m, // Convert from cents to dollars
                    currency = session.Currency
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [Route("webhook")]
        [ApiController]
        public class WebhookController : Controller
        {

            // This is your Stripe CLI webhook secret for testing your endpoint locally.
            const string endpointSecret = "whsec_b1af2c7090d74f5ef023920779a714edabefbc375edb5d12f633114c4106c7be";

            [HttpPost]
            public async Task<IActionResult> Index()
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                try
                {
                    var stripeEvent = EventUtility.ConstructEvent(json,
                        Request.Headers["Stripe-Signature"], endpointSecret);

                    // Handle the event
                    if (stripeEvent.Type == EventTypes.AccountUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.AccountApplicationAuthorized)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.AccountApplicationDeauthorized)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.AccountExternalAccountCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.AccountExternalAccountDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.AccountExternalAccountUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ApplicationFeeCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ApplicationFeeRefunded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ApplicationFeeRefundUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BalanceAvailable)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingAlertTriggered)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingCreditBalanceTransactionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingCreditGrantCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingCreditGrantUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingMeterCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingMeterDeactivated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingMeterReactivated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingMeterUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingPortalConfigurationCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingPortalConfigurationUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.BillingPortalSessionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CapabilityUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CashBalanceFundsAvailable)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeCaptured)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeExpired)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargePending)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeRefunded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeDisputeClosed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeDisputeCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeDisputeFundsReinstated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeDisputeFundsWithdrawn)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeDisputeUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ChargeRefundUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CheckoutSessionAsyncPaymentFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CheckoutSessionAsyncPaymentSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CheckoutSessionCompleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CheckoutSessionExpired)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateOrderCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateOrderCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateOrderDelayed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateOrderDelivered)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateOrderProductSubstituted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateProductCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ClimateProductPricingUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CouponCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CouponDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CouponUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CreditNoteCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CreditNoteUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CreditNoteVoided)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerDiscountCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerDiscountDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerDiscountUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSourceCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSourceDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSourceExpiring)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSourceUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionPaused)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionPendingUpdateApplied)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionPendingUpdateExpired)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionResumed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionTrialWillEnd)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerSubscriptionUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerTaxIdCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerTaxIdDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerTaxIdUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.CustomerCashBalanceTransactionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.EntitlementsActiveEntitlementSummaryUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FileCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountDeactivated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountDisconnected)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountReactivated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountRefreshedBalance)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountRefreshedOwnership)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.FinancialConnectionsAccountRefreshedTransactions)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IdentityVerificationSessionCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IdentityVerificationSessionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IdentityVerificationSessionProcessing)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IdentityVerificationSessionRequiresInput)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IdentityVerificationSessionVerified)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceFinalizationFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceFinalized)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceMarkedUncollectible)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceOverdue)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceOverpaid)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoicePaid)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoicePaymentActionRequired)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoicePaymentFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoicePaymentSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceSent)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceUpcoming)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceVoided)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceWillBeDue)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceItemCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.InvoiceItemDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingAuthorizationCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingAuthorizationUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingCardCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingCardUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingCardholderCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingCardholderUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingDisputeClosed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingDisputeCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingDisputeFundsReinstated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingDisputeFundsRescinded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingDisputeSubmitted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingDisputeUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingPersonalizationDesignActivated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingPersonalizationDesignDeactivated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingPersonalizationDesignRejected)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingPersonalizationDesignUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingTokenCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingTokenUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingTransactionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingTransactionPurchaseDetailsReceiptUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.IssuingTransactionUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.MandateUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentAmountCapturableUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentPartiallyFunded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentPaymentFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentProcessing)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentRequiresAction)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentIntentSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentLinkCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentLinkUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentMethodAttached)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentMethodAutomaticallyUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentMethodDetached)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PaymentMethodUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PayoutCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PayoutCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PayoutFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PayoutPaid)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PayoutReconciliationCompleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PayoutUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PersonCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PersonDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PersonUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PlanCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PlanDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PlanUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PriceCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PriceDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PriceUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ProductCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ProductDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ProductUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PromotionCodeCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.PromotionCodeUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.QuoteAccepted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.QuoteCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.QuoteCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.QuoteFinalized)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.RadarEarlyFraudWarningCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.RadarEarlyFraudWarningUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.RefundCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.RefundFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.RefundUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ReportingReportRunFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ReportingReportRunSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ReviewClosed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.ReviewOpened)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SetupIntentCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SetupIntentCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SetupIntentRequiresAction)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SetupIntentSetupFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SetupIntentSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SigmaScheduledQueryRunCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceChargeable)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceMandateNotification)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceRefundAttributesRequired)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceTransactionCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SourceTransactionUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleAborted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleCompleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleExpiring)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleReleased)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.SubscriptionScheduleUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TaxSettingsUpdated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TaxRateCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TaxRateUpdated)
                    {
                    }
                    else if (stripeEvent.Type == Stripe.EventTypes.TerminalReaderActionFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TerminalReaderActionSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TestHelpersTestClockAdvancing)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TestHelpersTestClockCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TestHelpersTestClockDeleted)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TestHelpersTestClockInternalFailure)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TestHelpersTestClockReady)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TopupCanceled)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TopupCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TopupFailed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TopupReversed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TopupSucceeded)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TransferCreated)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TransferReversed)
                    {
                    }
                    else if (stripeEvent.Type == EventTypes.TransferUpdated)
                    {
                    }
                    // ... handle other event types
                    else
                    {
                        Console.WriteLine("Unhandled event type: {0}", stripeEvent.Type);
                    }

                    return Ok();
                }
                catch (StripeException e)
                {
                    return BadRequest();
                }
            }
        }
    }
}

﻿using InnoBook.Enum;

namespace InnoBook.DTO.UserBusiness
{
    public class CreateUserBusinessDTO
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid? CompanyId { get; set; }
        public UserBusinessStatus Status { get; set; }
        public string? Role { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy {  get; set; }

    }
}

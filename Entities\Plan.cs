﻿using InnoBook.Enum;
using InnoLogiciel.Server.Entities;

namespace InnoBook.Entities
{
    public class Plan : BaseGuidEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal MonthlyPrice { get; set; }
        public decimal YearlyPrice { get; set; }
        public decimal AdditionalUserPrice { get; set; }
        public bool IsRecommended { get; set; }
        public int MaxClients { get; set; }
        public int MaxTeamMembers { get; set; }
        public bool HasUnlimitedInvoices { get; set; }
        public bool HasTimeTracking { get; set; }
        public bool HasCalendar { get; set; }
        public bool HasDashboard { get; set; }
        public bool HasExpenses { get; set; }
        public bool HasEstimates { get; set; }
        public bool HasIntegrations { get; set; }
        public bool HasReports { get; set; }
        public bool HasAccountingUser { get; set; }
        public bool HasContractingUser { get; set; }
        public bool HasUnlimitedUsers { get; set; }
        public bool HasUnlimitedClients { get; set; }
        public PlanEnum PlanType { get; set; }

        // Navigation properties
        public List<CompanyPlan> CompanyPlans { get; set; }
    }
}

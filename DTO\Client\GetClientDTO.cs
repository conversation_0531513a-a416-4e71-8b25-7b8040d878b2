﻿namespace InnoBook.DTO.Client
{
    public class GetClientDTO
    {
        public Guid CompanyId { get; set; }
        public string? ClientName { get; set; }
        public Guid Id { get; set; }

        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? EmailAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public string? BusinessPhoneNumber { get; set; }
        public string? MobilePhoneNumber { get; set; }
        public string? PostePhoneNumber { get; set; }
        public string? PosteBusinessPhoneNumber { get; set; }
        public string? PosteMobilePhoneNumber { get; set; }
        public string? Country { get; set; }
        public bool IsInternal { get; set; } = false;

        public string? AddressLine1 { get; set; }
        public string? AddressLine2 { get; set; }
        public string? TownCity { get; set; }
        public bool isActive { get; set; } = true;
        public string? StateProvince { get; set; }
        public string? PostalCode { get; set; }
    }
}

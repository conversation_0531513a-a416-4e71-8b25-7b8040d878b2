import { Component, OnInit, inject, DestroyRef } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SpinnerService } from 'app/service/spinner.service';
import { ToastService } from 'app/service/toast.service';
import { StripeService } from 'app/service/stripe.service';
import { PlanService } from 'app/service/plan.service';

interface Plan {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  isRecommended: boolean;
  maxClients: number;
  maxTeamMembers: number;
  additionalUserPrice: number;
  hasUnlimitedInvoices: boolean;
  hasTimeTracking: boolean;
  hasCalendar: boolean;
  hasDashboard: boolean;
  hasExpenses: boolean;
  hasEstimates: boolean;
  hasIntegrations: boolean;
  hasReports: boolean;
  hasAccountingUser: boolean;
  hasContractingUser: boolean;
  hasUnlimitedUsers: boolean;
  hasUnlimitedClients: boolean;
  planType: number;
  features: string[];
}

@Component({
  selector: 'app-plan-selection',
  standalone: true,
  imports: [
    SharedModule
  ],
  templateUrl: './plan-selection.component.html',
  styleUrl: './plan-selection.component.scss'
})
export class PlanSelectionComponent implements OnInit {
  public plans: Plan[] = [];
  public selectedPlan: Plan | null = null;
  public billingInterval: string = 'month';
  public additionalUsers: number = 0;
  public isLoading: boolean = false;

  private planService = inject(PlanService);
  private stripeService = inject(StripeService);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);
  private spinnerService = inject(SpinnerService);
  private toastService = inject(ToastService);

  ngOnInit(): void {
    this.loadPlans();
  }

  loadPlans(): void {
    this.isLoading = true;
    this.spinnerService.show();

    this.planService.getAllPlans()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (plans: Plan[]) => {
          this.plans = plans;
          // Generate features array for each plan based on properties
          this.plans.forEach(plan => {
            if (!plan.features || plan.features.length === 0) {
              plan.features = this.generateFeaturesFromPlan(plan);
            }
          });
          this.isLoading = false;
          this.spinnerService.hide();
        },
        error: (error) => {
          console.error('Error loading plans:', error);
          this.isLoading = false;
          this.spinnerService.hide();
          this.toastService.showError('Error', 'Failed to load plans');
        }
      });
  }

  /**
   * Generate features array from plan properties
   */
  private generateFeaturesFromPlan(plan: Plan): string[] {
    const features: string[] = [];

    // Client limits
    if (plan.hasUnlimitedClients) {
      features.push('Unlimited customers');
    } else if (plan.maxClients > 0) {
      features.push(`Up to ${plan.maxClients} customers`);
    }

    // Team member limits
    if (plan.hasUnlimitedUsers) {
      features.push('Unlimited users');
    } else if (plan.maxTeamMembers > 0) {
      features.push(`Up to ${plan.maxTeamMembers} team members`);
    }

    // Invoice features
    if (plan.hasUnlimitedInvoices) {
      features.push('Unlimited number of invoices');
    }

    // Core features
    if (plan.hasTimeTracking) features.push('Time tracking');
    if (plan.hasCalendar) features.push('Calendar');
    if (plan.hasDashboard) features.push('Dashboard');
    if (plan.hasExpenses) features.push('Expenses');
    if (plan.hasEstimates) features.push('Estimates');
    if (plan.hasIntegrations) features.push('Integrations');
    if (plan.hasReports) features.push('Reports');
    if (plan.hasAccountingUser) features.push('Accounting User');
    if (plan.hasContractingUser) features.push('Contracting User');

    // Additional user pricing
    if (plan.additionalUserPrice > 0) {
      features.push(`Additional $${plan.additionalUserPrice} per user per month`);
    }

    return features;
  }

  selectPlan(plan: Plan): void {
    if (plan.planType === 0) {
      // Cannot select free plan
      return;
    }
    this.selectedPlan = plan;
  }

  toggleBillingInterval(): void {
    this.billingInterval = this.billingInterval === 'month' ? 'year' : 'month';
  }

  getDisplayPrice(plan: Plan): number {
    return this.billingInterval === 'month' ? plan.monthlyPrice : plan.yearlyPrice / 12;
  }

  getTotalPrice(): number {
    if (!this.selectedPlan) return 0;

    const basePrice = this.getDisplayPrice(this.selectedPlan);
    const additionalUserPrice = this.selectedPlan.additionalUserPrice * this.additionalUsers;

    return basePrice + additionalUserPrice;
  }

  increaseAdditionalUsers(): void {
    this.additionalUsers++;
  }

  decreaseAdditionalUsers(): void {
    if (this.additionalUsers > 0) {
      this.additionalUsers--;
    }
  }

  subscribeToPlan(): void {
    if (!this.selectedPlan) {
      this.toastService.showError('Error', 'Please select a plan first');
      return;
    }

    this.spinnerService.show();

    // For paid plans, create a Stripe session
    const price = this.billingInterval === 'month'
      ? this.selectedPlan.monthlyPrice + (this.selectedPlan.additionalUserPrice * this.additionalUsers)
      : this.selectedPlan.yearlyPrice + (this.selectedPlan.additionalUserPrice * 12 * this.additionalUsers);

    this.stripeService.createPaymentSession(
      price,
      'USD',
      `${this.selectedPlan.name} Subscription`,
      'subscription',
      this.billingInterval,
      this.selectedPlan.id,
      this.additionalUsers
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: any) => {
          this.spinnerService.hide();

          // Store selected plan info in localStorage for retrieval after payment
          localStorage.setItem('selectedPlanId', this.selectedPlan!.id);
          localStorage.setItem('billingInterval', this.billingInterval);
          localStorage.setItem('additionalUsers', this.additionalUsers.toString());

          // Redirect to Stripe checkout page
          if (response && response.url) {
            window.location.href = response.url;
          } else {
            this.toastService.showError('Error', 'Failed to initialize payment');
          }
        },
        error: (error) => {
          this.spinnerService.hide();
          this.toastService.showError('Error', 'Failed to initialize payment: ' + error.message);
        }
      });
  }

  goBack(): void {
    this.router.navigate(['/billing']);
  }

  backToPlans(): void {
    this.selectedPlan = null;
  }
}

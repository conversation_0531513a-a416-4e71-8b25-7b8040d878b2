import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'app/module/shared.module';
import { StripeService } from 'app/service/stripe.service';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-stripe-test',
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    FormsModule
  ],
  template: `
    <div class="container-full mt-8">
      <div class="border border-border-primary rounded-md p-6">
        <h1 class="text-text-primary text-headline-lg-bold mb-4">Stripe Webhook Test</h1>

        <div class="mb-6">
          <h2 class="text-text-primary text-headline-sm-bold mb-2">Create Test Payment</h2>

          <div class="mb-4">
            <div class="flex items-center gap-4 mb-2">
              <input
                type="number"
                [(ngModel)]="amount"
                class="p-2 border border-border-primary rounded-md"
                placeholder="Amount"
              />
              <select
                [(ngModel)]="paymentType"
                class="p-2 border border-border-primary rounded-md"
              >
                <option value="one_time">One-time Payment</option>
                <option value="subscription">Subscription</option>
              </select>
            </div>

            <div *ngIf="paymentType === 'subscription'" class="flex items-center gap-4 mb-2">
              <div class="flex items-center">
                <input
                  type="radio"
                  id="monthly"
                  name="billingInterval"
                  value="month"
                  [(ngModel)]="billingInterval"
                  class="mr-2"
                />
                <label for="monthly">Monthly</label>
              </div>
              <div class="flex items-center">
                <input
                  type="radio"
                  id="yearly"
                  name="billingInterval"
                  value="year"
                  [(ngModel)]="billingInterval"
                  class="mr-2"
                />
                <label for="yearly">Yearly</label>
              </div>
            </div>

            <div class="mt-3">
              <button
                class="button-size-md button-primary"
                (click)="createPayment()"
                [disabled]="isLoading"
              >
                {{ paymentType === 'subscription' ? 'Create Subscription' : 'Create Payment' }}
              </button>
            </div>
          </div>

          <div *ngIf="paymentType === 'subscription'" class="p-3 bg-blue-50 text-blue-700 rounded-md text-sm">
            <p><strong>Note:</strong> Subscription payments will recur {{ billingInterval === 'month' ? 'monthly' : 'yearly' }} until cancelled.</p>
            <p>For testing, subscriptions will not actually charge your test card repeatedly.</p>
          </div>
        </div>

        <div class="mb-6">
          <h2 class="text-text-primary text-headline-sm-bold mb-2">Webhook Events</h2>
          <div class="border border-border-primary rounded-md p-4 bg-gray-50 h-64 overflow-auto">
            <div *ngFor="let event of webhookEvents" class="mb-2 p-2 border-b border-border-primary">
              <div class="flex justify-between">
                <span class="font-bold">{{ event.type }}</span>
                <span class="text-text-secondary text-sm">{{ event.timestamp | date:'medium' }}</span>
              </div>
              <pre class="text-sm mt-1 overflow-auto">{{ event.data | json }}</pre>
            </div>
            <div *ngIf="webhookEvents.length === 0" class="text-center text-text-secondary py-8">
              No webhook events received yet
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h2 class="text-text-primary text-headline-sm-bold mb-2">Simulate Webhook Event</h2>
          <div class="flex items-center gap-4 mb-4">
            <select
              [(ngModel)]="selectedEventType"
              class="p-2 border border-border-primary rounded-md"
            >
              <option value="checkout.session.completed">checkout.session.completed</option>
              <option value="payment_intent.succeeded">payment_intent.succeeded</option>
              <option value="payment_intent.payment_failed">payment_intent.payment_failed</option>
              <option value="customer.subscription.created">customer.subscription.created</option>
              <option value="customer.subscription.updated">customer.subscription.updated</option>
              <option value="invoice.payment_succeeded">invoice.payment_succeeded</option>
            </select>
            <button
              class="button-size-md button-primary"
              (click)="simulateWebhook()"
              [disabled]="isLoading"
            >
              Simulate Event
            </button>
          </div>
        </div>

        <div *ngIf="error" class="p-4 bg-red-100 text-red-700 rounded-md mb-4">
          {{ error }}
        </div>

        <div *ngIf="success" class="p-4 bg-green-100 text-green-700 rounded-md mb-4">
          {{ success }}
        </div>

        <div class="mt-6">
          <h2 class="text-text-primary text-headline-sm-bold mb-2">Test Card Numbers</h2>
          <div class="overflow-x-auto">
            <table class="min-w-full border border-border-primary">
              <thead>
                <tr class="bg-gray-50">
                  <th class="p-2 border-b border-border-primary text-left">Card Type</th>
                  <th class="p-2 border-b border-border-primary text-left">Number</th>
                  <th class="p-2 border-b border-border-primary text-left">CVC</th>
                  <th class="p-2 border-b border-border-primary text-left">Expiry</th>
                  <th class="p-2 border-b border-border-primary text-left">Result</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="p-2 border-b border-border-primary">Visa</td>
                  <td class="p-2 border-b border-border-primary font-mono">4242 4242 4242 4242</td>
                  <td class="p-2 border-b border-border-primary">Any 3 digits</td>
                  <td class="p-2 border-b border-border-primary">Any future date</td>
                  <td class="p-2 border-b border-border-primary text-green-600">Success</td>
                </tr>
                <tr>
                  <td class="p-2 border-b border-border-primary">Visa</td>
                  <td class="p-2 border-b border-border-primary font-mono">4000 0025 0000 3155</td>
                  <td class="p-2 border-b border-border-primary">Any 3 digits</td>
                  <td class="p-2 border-b border-border-primary">Any future date</td>
                  <td class="p-2 border-b border-border-primary">Requires authentication</td>
                </tr>
                <tr>
                  <td class="p-2 border-b border-border-primary">Visa</td>
                  <td class="p-2 border-b border-border-primary font-mono">4000 0000 0000 0002</td>
                  <td class="p-2 border-b border-border-primary">Any 3 digits</td>
                  <td class="p-2 border-b border-border-primary">Any future date</td>
                  <td class="p-2 border-b border-border-primary text-red-600">Declined</td>
                </tr>
                <tr>
                  <td class="p-2 border-b border-border-primary">Mastercard</td>
                  <td class="p-2 border-b border-border-primary font-mono">5555 5555 5555 4444</td>
                  <td class="p-2 border-b border-border-primary">Any 3 digits</td>
                  <td class="p-2 border-b border-border-primary">Any future date</td>
                  <td class="p-2 border-b border-border-primary text-green-600">Success</td>
                </tr>
                <tr>
                  <td class="p-2 border-b border-border-primary">Link</td>
                  <td class="p-2 border-b border-border-primary font-mono">4242 4242 4242 4242</td>
                  <td class="p-2 border-b border-border-primary">Any 3 digits</td>
                  <td class="p-2 border-b border-border-primary">Any future date</td>
                  <td class="p-2 border-b border-border-primary">Use email with +success&#64;example.com</td>
                </tr>
              </tbody>
            </table>
          </div>
          <p class="text-text-secondary text-sm mt-2">
            For more test cards, see the <a href="https://stripe.com/docs/testing" target="_blank" class="text-blue-600 hover:underline">Stripe testing documentation</a>.
          </p>
        </div>
      </div>
    </div>
  `
})
export class StripeTestComponent implements OnInit {
  amount = 55;
  isLoading = false;
  error = '';
  success = '';
  webhookEvents: any[] = [];
  selectedEventType = 'checkout.session.completed';
  paymentType = 'one_time'; // 'one_time' or 'subscription'
  billingInterval = 'month'; // 'month' or 'year'

  private stripeService = inject(StripeService);
  private http = inject(HttpClient);

  ngOnInit() {
    // Load webhook events from local storage
    const savedEvents = localStorage.getItem('stripe-webhook-events');
    if (savedEvents) {
      this.webhookEvents = JSON.parse(savedEvents);
    }
  }

  createPayment() {
    this.isLoading = true;
    this.error = '';
    this.success = '';

    // Create description based on payment type
    let description = this.paymentType === 'subscription'
      ? `${this.billingInterval === 'month' ? 'Monthly' : 'Yearly'} Subscription`
      : 'One-time Payment';

    this.stripeService.createPaymentSession(
      this.amount,
      'USD',
      description,
      this.paymentType,
      this.billingInterval
    )
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response && response.url) {
            this.success = `${this.paymentType === 'subscription' ? 'Subscription' : 'Payment'} session created! Redirecting to Stripe...`;
            window.open(response.url, '_blank');
          } else {
            this.error = `Failed to create ${this.paymentType === 'subscription' ? 'subscription' : 'payment'} session`;
          }
        },
        error: (err) => {
          this.isLoading = false;
          this.error = `Error: ${err.message || 'Unknown error'}`;
        }
      });
  }

  simulateWebhook() {
    this.isLoading = true;
    this.error = '';
    this.success = '';

    // Create a mock webhook event
    let objectType = 'payment_intent';
    let objectId = `pi_${Math.random().toString(36).substring(2, 15)}`;

    if (this.selectedEventType.includes('checkout')) {
      objectType = 'checkout.session';
      objectId = `cs_${Math.random().toString(36).substring(2, 15)}`;
    } else if (this.selectedEventType.includes('subscription')) {
      objectType = 'subscription';
      objectId = `sub_${Math.random().toString(36).substring(2, 15)}`;
    } else if (this.selectedEventType.includes('invoice')) {
      objectType = 'invoice';
      objectId = `in_${Math.random().toString(36).substring(2, 15)}`;
    }

    // Create a base mock event
    const mockEvent: any = {
      id: `evt_${Math.random().toString(36).substring(2, 15)}`,
      type: this.selectedEventType,
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: objectId,
          object: objectType,
          amount: this.amount * 100,
          currency: 'usd',
          status: this.selectedEventType.includes('failed') ? 'failed' : 'succeeded',
          metadata: {
            companyId: 'test_company',
            userId: 'test_user'
          }
        }
      }
    };

    // Add subscription-specific fields
    if (objectType === 'subscription') {
      // Cast to any to avoid TypeScript errors when adding properties
      const subscriptionObject = mockEvent.data.object as any;
      subscriptionObject.current_period_start = Math.floor(Date.now() / 1000);
      subscriptionObject.current_period_end = Math.floor(Date.now() / 1000) + (this.billingInterval === 'month' ? 30 * 24 * 60 * 60 : 365 * 24 * 60 * 60);
      subscriptionObject.plan = {
        interval: this.billingInterval,
        amount: this.amount * 100
      };
    }

    // Add invoice-specific fields
    if (objectType === 'invoice') {
      // Cast to any to avoid TypeScript errors when adding properties
      const invoiceObject = mockEvent.data.object as any;
      invoiceObject.subscription = `sub_${Math.random().toString(36).substring(2, 15)}`;
      invoiceObject.total = this.amount * 100;
      invoiceObject.billing_reason = 'subscription_cycle';
    }

    // Send the mock event to the webhook endpoint
    this.http.post(`${environment.HOST_API}/api/Stripe/Webhook`, mockEvent)
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          this.success = `Webhook event ${this.selectedEventType} simulated successfully`;

          // Add to webhook events
          this.webhookEvents.unshift({
            type: this.selectedEventType,
            timestamp: new Date(),
            data: mockEvent
          });

          // Save to local storage
          localStorage.setItem('stripe-webhook-events', JSON.stringify(this.webhookEvents.slice(0, 10)));
        },
        error: (err) => {
          this.isLoading = false;
          this.error = `Error: ${err.message || 'Unknown error'}`;

          // Still add to webhook events for debugging
          this.webhookEvents.unshift({
            type: this.selectedEventType,
            timestamp: new Date(),
            data: mockEvent,
            error: err.message
          });

          // Save to local storage
          localStorage.setItem('stripe-webhook-events', JSON.stringify(this.webhookEvents.slice(0, 10)));
        }
      });
  }
}

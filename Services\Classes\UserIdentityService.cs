﻿using InnoBook.DTO;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.TimeTracking;
using InnoBook.DTO.User;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Model;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using InnoLogiciel.Server.Enum.User;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace InnoBook.Services.Classes
{
    public class UserIdentityService(InnoLogicielContext context, IConfiguration configuration, IPasswordHasher passwordHasher) : IUserIdentity
    {
        public ClaimsPrincipal GetPrincipalFromExpiredToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateAudience = false, //you might want to validate the audience and issuer depending on your use case
                ValidateIssuer = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey"))),
                ValidateLifetime = true //here we are saying that we don't care about the token's expiration date
            };
            var tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken securityToken;
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out securityToken);
            var jwtSecurityToken = securityToken as JwtSecurityToken;
            if (jwtSecurityToken == null)
                throw new SecurityTokenException("Invalid token");
            return principal;
        }


        public ClaimsPrincipal GetPrincipalRefreshToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateAudience = false, //you might want to validate the audience and issuer depending on your use case
                ValidateIssuer = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey"))),
                ValidateLifetime = true //here we are saying that we don't care about the token's expiration date
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken securityToken;
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out securityToken);
            var jwtToken = securityToken as JwtSecurityToken;

            if (jwtToken == null || !jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                throw new SecurityTokenException("Invalid token");
            }

            return principal;
        }
        public string? GetIdCompanyFromRefreshToken(string refreshToken)
        {

            try
            {
                var handler = new JwtSecurityTokenHandler();

                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateAudience = false, //you might want to validate the audience and issuer depending on your use case
                    ValidateIssuer = false,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey"))),
                    ValidateLifetime = true
                };

                var tokenHandler = new JwtSecurityTokenHandler();
                SecurityToken securityToken;
                var principal = tokenHandler.ValidateToken(refreshToken, tokenValidationParameters, out securityToken);
                var jwtToken = securityToken as JwtSecurityToken;

                if (jwtToken == null)
                {
                    return null;
                }

                // Clone existing claims and add the new claim
                var claims = new List<Claim>(principal.Claims);
                var companyId = "";
                if (claims.Any(c => c.Type == "companyId"))
                    companyId = claims.Single(c => c.Type == "companyId").Value.ToString();
                return companyId;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        //public string GenerateRefreshToken()
        //{
        //    var randomNumber = new byte[32];
        //    using (var rng = RandomNumberGenerator.Create())
        //    {
        //        rng.GetBytes(randomNumber);
        //        return Convert.ToBase64String(randomNumber);
        //    }
        //}

        public bool IsTokenExpired(string refreshToken)
        {
            var tokenHandler = new JwtSecurityTokenHandler();

            // Check if the token is a valid JWT
            if (tokenHandler.CanReadToken(refreshToken))
            {
                var jwtToken = tokenHandler.ReadToken(refreshToken) as JwtSecurityToken;

                // Extract the expiration time from the token
                var expirationDate = jwtToken.ValidTo;

                // Check if the token has expired
                return expirationDate > DateTime.UtcNow;
            }
            else
            {
                // Token is invalid or unreadable
                throw new ArgumentException("Invalid refresh token format.");
            }
        }
        public string GenerateAccessToken(IEnumerable<Claim> claims)
        {
            var secretKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey")));
            var signinCredentials = new SigningCredentials(secretKey, SecurityAlgorithms.HmacSha256);
            var tokeOptions = new JwtSecurityToken(
                issuer: configuration.GetRequired("Jwt:Issuer"),
                audience: configuration.GetRequired("Jwt:Audience"),
                claims: claims,
                expires: DateTime.Now.AddMinutes(30),
                signingCredentials: signinCredentials
            );
            var tokenString = new JwtSecurityTokenHandler().WriteToken(tokeOptions);
            return tokenString;
        }
        public string GenerateRefreshToken(IEnumerable<Claim> claims)
        {
            var secretKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey")));
            var signinCredentials = new SigningCredentials(secretKey, SecurityAlgorithms.HmacSha256);
            var tokeOptions = new JwtSecurityToken(
                issuer: configuration.GetRequired("Jwt:Issuer"),
                audience: configuration.GetRequired("Jwt:Audience") ,
                claims: claims,
                expires: DateTime.Now.AddDays(14),
                signingCredentials: signinCredentials
            );
            var tokenString = new JwtSecurityTokenHandler().WriteToken(tokeOptions);
            return tokenString;
        }
        public async Task<Jwt?> RefreshToken(string refreshToken, string companyId)
        {

            var handler = new JwtSecurityTokenHandler();
            var tokenS = handler.ReadJwtToken(refreshToken);

            var _id = tokenS.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
            var businessRole = await context.UserBusinesses.FirstOrDefaultAsync(c => c.CompanyId.ToString() == companyId && c.UserId.ToString() == _id);

            if (string.IsNullOrEmpty(_id))
            {
                return null;
            }
            var user = await context.Users.FirstOrDefaultAsync(x => x.Id.ToString() == _id);

            {

                if (user == null) return null;
                var claims = new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Role, user.Role ?? UserRole.User),
                    new Claim("companyId", companyId),
                    new Claim("businessRole", businessRole?.Role ?? "")
                };
                var claimsRefresh = new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim("companyId", companyId)
                };
                var dt = new Jwt
                {
                    accessToken = GenerateAccessToken(claims),
                    refreshToken = GenerateRefreshToken(claimsRefresh),
                };
                return dt;
            }
        }

        public IActionResult RecoverPassword(string email)
        {
            if (!string.IsNullOrEmpty(email))
            {
                var result = context.Users.FirstOrDefault(x => x.Email.Equals(email));
                if (result == null)
                {
                    return new BadRequestObjectResult("general.error.sendEmail");
                }
                //var mail = new MailInfoAndContent()
                //{
                //    From = configuration.GetSection("Smtp:From").Value,
                //    Subject = "Récupération mot de passe",
                //    To = email,
                //    Body = "<a href='" + linkCheckerService.GenerateUrl(email, LinkCheckerType.RECOVER_PASS) + "'>Cliquez ici<a/> pour pouvoir récupérer votre mot de passe : "
                //};
                //var res = mailService.SendMail(mail);

                //    if (res is BadRequestObjectResult)
                //        return new BadRequestObjectResult("general.error.sendEmail");
                //    else
                //        msg = new OkObjectResult("User.Recover.Password.Sucessfully");
                //}
                //else

                //else
            }
            return new BadRequestObjectResult("User.Recover.Password.Unsucessfully2");
        }

        public async Task<Jwt> SignIn(string email)
        {

            var user = await context.Users.FirstOrDefaultAsync(x => x.Email == email);
            if (user == null) return null;
            var claimsRefresh = new[]
            {
                 new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            };
            var companyId = context.UserBusinesses
                .Where(ub => ub.UserId == user.Id)
                .Select(ub => ub.CompanyId).FirstOrDefault();
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Role, user.Role ?? UserRole.User),
                new Claim("companyId", companyId.ToString())
            };

            var dt = new Jwt
            {
                accessToken = GenerateAccessToken(claims),
                refreshToken = GenerateRefreshToken(claimsRefresh),
            };
            return dt;
        }

        public async Task<string> CreateTokenRecoverPassword(string email)
        {
            var user = await context.Users.FirstAsync(x => x.Email == email);

            var randomNumber = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomNumber);
            }
            string token = Convert.ToBase64String(randomNumber);

            user.PasswordResetToken = token;
            user.ExpireTime = DateTime.UtcNow.AddMinutes(15);
            context.SaveChanges();

            return token;
        }

        public async Task<string> GenerateUrlRecoverPass(string email)
        {
            email = email.ToLower().Trim();
            var url = configuration.GetRequired("ResetPasswordUrl");
            var accessToken = await CreateTokenRecoverPassword(email);
            return url + "?token=" + accessToken;
        }

        public async Task<Jwt> Login(string email, string pass)
        {
            var user = await context.Users.FirstOrDefaultAsync(x => x.Email == email);

            // Handle for case user has been invited to company but has not set up password yet
            if (user == null || user.Password == null) throw new Exception("Username & password is incorrect");

            bool checkVerify = passwordHasher.Verify(pass, user.Password);
            if (checkVerify == false) throw new Exception("Username & password is incorrect");

            if (user.Status != UserStatus.Active) throw new Exception("Your account is not active");

            var claimsRefresh = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString())
            };

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Role, user.Role ?? UserRole.User),
                new Claim("companyId", string.Empty)

            };

            var dt = new Jwt
            {
                accessToken = GenerateAccessToken(claims),
                refreshToken = GenerateRefreshToken(claimsRefresh),
            };


            return dt;
        }

        public async Task<bool> DeleteUser(string userId)
        {
            // Delete account permanently
            // TODO: change to soft delete to avoid missing data in invoice
            using (var transaction = context.Database.BeginTransaction())
            {
                try
                {
                    var listItemUseMember = context.Members.Where(x => x.UserId == Guid.Parse(userId)).ToList();
                    // remove timetracking
                    var memberIdsToDelete = listItemUseMember.Select(m => m.Id).ToList();
                    var listItemTimeTracking = context.TimeTrackings.Where(x => memberIdsToDelete.Contains(x.MemberId.Value)).ToList();
                    context.TimeTrackings.RemoveRange(listItemTimeTracking);

                    // remove usermember
                    context.Members.RemoveRange(listItemUseMember);


                    // remove ItemInvoice
                    var listItemUserItemInvoice = context.ItemInvoice.Where(x => x.UserId == Guid.Parse(userId)).ToList();
                    context.ItemInvoice.RemoveRange(listItemUserItemInvoice);

                    // remove UserBusinesses
                    var listItemUserBussines = context.UserBusinesses.Where(x => x.UserId == Guid.Parse(userId)).ToList();
                    context.UserBusinesses.RemoveRange(listItemUserBussines);

                    // remove Users
                    var listItemDetail = context.Users.Where(x => x.Id == Guid.Parse(userId)).ToList();
                    context.Users.RemoveRange(listItemDetail);
                    await context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    return true;
                }
                catch
                {

                    return false;
                }

            }

        }

        public async Task<PaginatedResponse<GetUserDTO>> GetAllUser(PaginatedRequest query)
        {
            var total = await context.Users.CountAsync();
            var data = await context.Users
                                  .Select(u => new GetUserDTO
                                  {
                                      Id=u.Id,
                                      FirstName = u.FirstName,
                                      LastName = u.LastName,
                                      Email = u.Email
                                  })
                                .Skip((query.Page - 1) * query.PageSize)
                                .Take(query.PageSize)
                                .ToListAsync();

            return new PaginatedResponse<GetUserDTO>
            {
                Data = data,
                Page = query.Page,
                TotalPage = data.Count(),
                PageSize = query.PageSize,
                TotalRecords = total
            };
        }

        public async Task<GetUserDTO> FindUserEmailAndCode(string email, string code)
        {
            return await context.Users
              .Where(x => x.Email.Equals(email) && x.TwoFactorCode == code)
              .Select(u => new GetUserDTO
              {
                  Id = u.Id,
                  Status = u.Status,
                  FirstName = u.FirstName,
                  LastName = u.LastName,
                  Email = u.Email,
                  Username = u.Username,
                  ExpireTime = u.ExpireTime,
                  TwoFactorCode = u.TwoFactorCode,
                  TimeZoneId = u.TimeZoneId
              })
              .FirstOrDefaultAsync();
                }

        public async Task<GetAllDataUserDTO> FindUserEmail(string email)
        {
            var user = await context.Users
      .Where(x => x.Email == email)
      .Select(x => new GetAllDataUserDTO
      {
          Id = x.Id,
          Status = x.Status,
          Password=x.Password,
          UpdatedAt=x.UpdatedAt,
          UpdatedBy=x.UpdatedBy,
          Role = x.Role,
          FirstName = x.FirstName,
          LastName = x.LastName,
          Email = x.Email,
          Username = x.Username,
          ExpireTime = x.ExpireTime,
          TwoFactorCode = x.TwoFactorCode,
          TimeZoneId = x.TimeZoneId
      })
      .FirstOrDefaultAsync();
            return user;

        }

        public async Task<GetUserDTO> FindId(Guid Id)
        {
            return await context.Users
             .Where(x => x.Id.Equals(Id))
             .Select(u => new GetUserDTO
             {
                 Id = u.Id,
                 Status = u.Status,
                 FirstName = u.FirstName,
                 LastName = u.LastName,
                 Email = u.Email,
                 Username = u.Username,
                 ExpireTime = u.ExpireTime,
                 TwoFactorCode = u.TwoFactorCode,
                 TimeZoneId = u.TimeZoneId
             })
             .FirstOrDefaultAsync();
        }

        public async Task<GetUserProfileDTO> GetUserProfile(Guid id)
        {
            var user = await context.Users.FirstOrDefaultAsync(x => x.Id == id);

            if (user == null)
                throw new KeyNotFoundException($"User with ID '{id}' was not found.");

            return new GetUserProfileDTO
            {
                FirstName = user.FirstName,
                LastName = user.LastName,
                UserName = user.Username,
                Email = user.Email,
                TimeZoneId = user.TimeZoneId,
                Id = user.Id.ToString(),
            };
        }



        public async Task<List<GetUserDTO>> ListUser()
        {
            return await context.Users
        .Select(u => new GetUserDTO
        {
            Id = u.Id,
            Status = u.Status,
            FirstName = u.FirstName,
            LastName = u.LastName,
            Email = u.Email,
            Username = u.Username,
            ExpireTime = u.ExpireTime,
            TwoFactorCode = u.TwoFactorCode,
            TimeZoneId = u.TimeZoneId
        })
        .ToListAsync();
        }

        public async Task<bool> Update(UserUpdateDTO userDto)
        {
            var user = await context.Users.FindAsync(userDto.Id);
            if (user == null)
            {
                throw new Exception("User not found.");
            }
            user.FirstName = userDto.FirstName;
            user.LastName = userDto.LastName;
            user.Email = userDto.Email;
            user.Status = UserStatus.Active;
            user.IsRunning = userDto.IsRunning ?? user.IsRunning;
            user.Timer = userDto.Timer;
            user.TimerStartTime = userDto.TimerStartTime;
            user.Username = userDto.Username;
            user.Is2FAEnabled = userDto.Is2FAEnabled;
            user.TwoFactorCode = userDto.TwoFactorCode;
            user.ExpireTime = userDto.ExpireTime;
            user.Status = userDto.Status;
            user.LastLoginTime = userDto.LastLoginTime;
            user.PasswordResetToken = userDto.PasswordResetToken;
            user.TokenExpiration = userDto.TokenExpiration;
            user.TimeZoneId = userDto.TimeZoneId;
            user.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> Update(string email, UserUpdateDTO userDto)
        {
            var user = await context.Users.FirstOrDefaultAsync(c => c.Email == email);
            if (user == null)
            {
                throw new Exception("User not found.");
            }
            user.FirstName = userDto.FirstName;
            user.LastName = userDto.LastName;
            user.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();
            return true;
        }

        // Dans votre service Identity
        public async Task<bool> UpdateTwoFactorAsync(Guid userId, string code, DateTime expireTime)
        {
            var user = await context.Users.FindAsync(userId);
            if (user == null)
                return false;

            user.TwoFactorCode = code;
            user.ExpireTime = expireTime;
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<Guid> Insert(CreateUserDTO user)
        {
            var createUser = new User()
            {
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email=user.Email,
                Status = UserStatus.Active,
                Password = user.Password,
                UpdatedAt = DateTime.UtcNow,
                TimeZoneId = user.TimeZoneId,
                ExpireTime= user.ExpireTime,
                Role = user.Role?? UserRole.User,
                CreatedAt = DateTime.UtcNow,

            };

            context.Users.Add(createUser);
            await context.SaveChangesAsync();
            return createUser.Id;
        }

        public async Task<Jwt> Register(RegisterUserDTO userDto)
        {
            var claims = new List<Claim>();
            var claimsRefresh = new List<Claim>();
            var user = await FindUserEmail(userDto.Email);
            string hashPass = passwordHasher.Hash(userDto.Password);

            if (string.IsNullOrEmpty(userDto.Token))
            {
                // Normal case
                if (user != null)
                {
                    throw new Exception("Email already register");
                }
                var createUser = new CreateUserDTO()
                {
                    FirstName = userDto.FirstName,
                    LastName = userDto.LastName,
                    Email=userDto.Email,
                    Password = hashPass,
                    TimeZoneId = userDto.TimeZoneId,
                    Role = UserRole.User,
                };
               var IdUser= await Insert(createUser);
                claims.AddRange(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, IdUser.ToString()),
                    new Claim(ClaimTypes.Role, createUser.Role ?? UserRole.User),
                });
                claimsRefresh.Add(new Claim(ClaimTypes.NameIdentifier, IdUser.ToString()));
            }
            else
            {
                // Invitation case
                var tokenResult = ValidateInvitationToken(userDto.Token, userDto.Email);
                using (var transaction = context.Database.BeginTransaction())
                {

                    try
                    {
                        if (user == null || user.Status != UserStatus.Invite)
                        {
                            throw new Exception("Invalid invitation");
                        }
                        user.FirstName = userDto.FirstName;
                        user.LastName = userDto.LastName;
                        user.Status = UserStatus.Active;
                        user.Password = hashPass;
                        user.UpdatedAt = DateTime.UtcNow;
                        user.UpdatedBy = user.Id.ToString();
                        user.TimeZoneId = userDto.TimeZoneId ?? user.TimeZoneId;
                        user.Role = UserRole.User;

                        var userBusiness = await context.UserBusinesses
                                                    .Where(c => c.CompanyId == tokenResult.CompanyId && c.UserId == user.Id).FirstOrDefaultAsync();

                        userBusiness.Status = UserBusinessStatus.Active;
                        userBusiness.UpdatedAt = DateTime.UtcNow;
                        userBusiness.UpdatedBy = user.Id.ToString();

                        await context.SaveChangesAsync();
                        await transaction.CommitAsync();

                        claims.AddRange(new[] 
                        {
                            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                            new Claim(ClaimTypes.Role, user.Role ?? UserRole.User),
                            new Claim("companyId", userBusiness?.CompanyId.ToString() ?? ""),
                            new Claim("businessRole", userBusiness?.Role ?? "")
                        });

                        claimsRefresh.AddRange(new[]
                        {
                            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                            new Claim("companyId", userBusiness?.CompanyId.ToString() ?? "")
                        });
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        throw;
                    }
                }
            }
            return new Jwt()
            {
                accessToken = GenerateAccessToken(claims),
                refreshToken = GenerateRefreshToken(claimsRefresh),
            };
        }

        public async Task<Jwt> UpdateAccessToken(string token, string newClaimType, string newClaimValue)
        {

            var handler = new JwtSecurityTokenHandler();

            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateAudience = false, //you might want to validate the audience and issuer depending on your use case
                ValidateIssuer = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey"))),
                ValidateLifetime = false //here we are saying that we don't care about the token's expiration date
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken securityToken;
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out securityToken);
            var jwtToken = securityToken as JwtSecurityToken;

            if (jwtToken == null)
            {
                return null;
            }

            // Clone existing claims and add the new claim
            var claims = new List<Claim>(principal.Claims);
            var existingClaim = claims.FirstOrDefault(c => c.Type == "companyId");
            var existingClaimBusinessRole = claims.FirstOrDefault(c => c.Type == "businessRole");
            var UserId = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            var businessRole = await context.UserBusinesses.FirstOrDefaultAsync(c => c.CompanyId.ToString() == newClaimValue && c.UserId.ToString() == UserId);

            if (existingClaim != null)
            {
                // Update the value if the claim exists
                claims.Remove(existingClaim);
                claims.Add(new Claim(newClaimType, newClaimValue));
            }
            else
            {
                // Add the claim if it does not exist
                claims.Add(new Claim(newClaimType, newClaimValue));
            }
            if (existingClaimBusinessRole != null)
            {
                // Update the value if the claim exists
                claims.Remove(existingClaimBusinessRole);
                claims.Add(new Claim("businessRole", businessRole?.Role ?? ""));
            }
            else
            {
                // Add the claim if it does not exist
                claims.Add(new Claim("businessRole", businessRole?.Role ?? ""));
            }
            // Create a new token with the updated claims
            var secretKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey")));
            var signinCredentials = new SigningCredentials(secretKey, SecurityAlgorithms.HmacSha256);
            var tokeOptions = new JwtSecurityToken(
               issuer: configuration.GetRequired("Jwt:Issuer"),
               claims: claims,
               expires: DateTime.Now.AddMinutes(30),
               signingCredentials: signinCredentials
            );
            var tokenString = new JwtSecurityTokenHandler().WriteToken(tokeOptions);
            var claimsRefresh = new[]
              {
                    new Claim(ClaimTypes.NameIdentifier, UserId.ToString()),
                      new Claim(newClaimType, newClaimValue),
                };
            var dt = new Jwt
            {
                accessToken = tokenString,
                refreshToken = GenerateRefreshToken(claimsRefresh),
            };
            return dt;
        }

        public async Task UpdateTimer(UpdateTimerDTO user, Guid UserId)
        {
            var data = await context.Users.FirstOrDefaultAsync(x => x.Id.Equals(UserId));
            if (data != null)
            {
                data.IsRunning = user.IsRunning != null ? user.IsRunning : data.IsRunning;
                data.Timer = user.Timer;
                data.TimerStartTime = user.TimerStartTime ?? null;
                await context.SaveChangesAsync();
            }

        }

        public async Task<CheckTimerDTO?> CheckTimer(Guid Id)
        {
            var data = await context.Users.FirstOrDefaultAsync(x => x.Id.Equals(Id));
            if (data != null)
            {
                var dt = new CheckTimerDTO
                {
                    IsRunning = data.IsRunning,
                    Timer = data.Timer,
                    TimerStartTime = data.TimerStartTime
                };

                return dt;

            }
            return null;
        }

        public bool ChangePassWithToken(TokenPass tokenPass)
        {
            var check_user = context.Users.FirstOrDefault(x => x.PasswordResetToken.Equals(tokenPass.token));
            if (check_user == null || check_user.ExpireTime < DateTime.UtcNow)
            {
                return false;
            }
            check_user.Password = passwordHasher.Hash(tokenPass.pass);
            context.Users.Update(check_user);
            context.SaveChanges();
            return true;
        }

        public User UserByBusinessId(string businessId)
        {
            var total = context.UserBusinesses.Count(c => c.Id == Guid.Parse(businessId));
            if (total == 0)
                throw new Exception("User not found");

            var user = context.UserBusinesses.Where(c => c.Id == Guid.Parse(businessId))
                .Include(c => c.User).Include(c => c.Company).
                Select(c => new User
                {
                    Email = c.User.Email,
                    //FirstName = c.User.FirstName,
                    //LastName = c.User.LastName,
                    Company = c.Company.BusinessName
                });
            return user.Single();
        }

        public UserBusinessStatus GetUserStatus(string businessId)
        {
            var status = context.UserBusinesses.Where(c => c.Id == Guid.Parse(businessId))
                .Include(c => c.User).Select(c => c.Status);
            return status.Single();
        }

        public async Task<ValidateInvitationResponse> ValidateInvitation(ValidateInvitationDto invitation)
        {
            var tokenResult = ValidateInvitationToken(invitation.Token, invitation.Email);
            // Find user
            var user = await context.UserBusinesses.Include(c => c.User)
                                          .Select(c => new {
                                              Email = c.User.Email,
                                              Status = c.User.Status,
                                              CompanyId = c.CompanyId,
                                              BusinessId = c.Id,
                                              Id = c.UserId,
                                              Role = c.Role
                                          }).FirstOrDefaultAsync(x => x.Email == tokenResult.Email && x.CompanyId == tokenResult.CompanyId);
            if (user == null)
            {
                throw new Exception("User does not found");
            }

            // User is waiting for invitation
            if (user.Status == UserStatus.Invite)
            {
                return new ValidateInvitationResponse() 
                { 
                    BusinessId = user.BusinessId.ToString(), 
                    ValidAccount = false 
                };
            }

            // user is already registered and not active
            if (user.Status != UserStatus.Active)
            {
                throw new Exception("Your account has been deactivated");
            }

            // Send login token to pass register
            return new ValidateInvitationResponse()
            {
                UserId = user.Id.ToString(),
                BusinessId = user.BusinessId.ToString(),
                ValidAccount = true
            };
        }

        private ValidateInvitationTokenResult ValidateInvitationToken(string token, string email)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateAudience = false, //you might want to validate the audience and issuer depending on your use case
                ValidateIssuer = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetRequired("Jwt:SecretKey"))),
                ValidateLifetime = false //here we are saying that we don't care about the token's expiration date
            };

            SecurityToken validatedToken;
            ClaimsPrincipal principal;
            try
            {
                principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out validatedToken);
            }
            catch (SecurityTokenException ex)
            {
                throw new SecurityTokenException("Invalid token", ex);
            }

            // Validate expired time
            if (validatedToken.ValidTo <= DateTime.UtcNow)
            {
                throw new SecurityTokenException("Token expired");
            }

            // Extract email from token claims
            var emailFromToken = principal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value;
            if (emailFromToken == null || emailFromToken != email)
            {
                throw new SecurityTokenException("Invalid token or email mismatch.");
            }

            // Extract companyId from token claims
            var companyIdStr = principal.Claims.FirstOrDefault(c => c.Type == "companyId")?.Value;
            if (companyIdStr == null || !Guid.TryParse(companyIdStr, out Guid companyId))
            {
                throw new SecurityTokenException("Invalid companyId.");
            }

            return new ValidateInvitationTokenResult 
            { 
                CompanyId = companyId,
                Email = emailFromToken
            };
        }
    }
}

﻿using InnoBook.Enum;
using InnoLogiciel.Server.Entities;

namespace InnoBook.Entities
{
    public class CompanyPlan : BaseGuidEntity
    {
        public Guid CompanyId { get; set; }
        public Guid PlanId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public CompanyPlanStatus Status { get; set; }
        public RenewTypeEnum RenewType { get; set; }
        public string SubscriptionId { get; set; } // Stripe subscription ID
        public int AdditionalUsers { get; set; }
        public decimal TotalPrice { get; set; }

        // Navigation properties
        public Company Company { get; set; }
        public Plan Plan { get; set; }
    }
}

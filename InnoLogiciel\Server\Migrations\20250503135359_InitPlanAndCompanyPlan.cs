﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace InnoLogiciel.Server.Migrations
{
    /// <inheritdoc />
    public partial class InitPlanAndCompanyPlan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "stripe_customer_id",
                table: "companies",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "plans",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false, defaultValueSql: "gen_random_uuid()"),
                    name = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    monthly_price = table.Column<decimal>(type: "numeric", nullable: false),
                    yearly_price = table.Column<decimal>(type: "numeric", nullable: false),
                    additional_user_price = table.Column<decimal>(type: "numeric", nullable: false),
                    is_recommended = table.Column<bool>(type: "boolean", nullable: false),
                    max_clients = table.Column<int>(type: "integer", nullable: false),
                    max_team_members = table.Column<int>(type: "integer", nullable: false),
                    has_unlimited_invoices = table.Column<bool>(type: "boolean", nullable: false),
                    has_time_tracking = table.Column<bool>(type: "boolean", nullable: false),
                    has_calendar = table.Column<bool>(type: "boolean", nullable: false),
                    has_dashboard = table.Column<bool>(type: "boolean", nullable: false),
                    has_expenses = table.Column<bool>(type: "boolean", nullable: false),
                    has_estimates = table.Column<bool>(type: "boolean", nullable: false),
                    has_integrations = table.Column<bool>(type: "boolean", nullable: false),
                    has_reports = table.Column<bool>(type: "boolean", nullable: false),
                    has_accounting_user = table.Column<bool>(type: "boolean", nullable: false),
                    has_contracting_user = table.Column<bool>(type: "boolean", nullable: false),
                    has_unlimited_users = table.Column<bool>(type: "boolean", nullable: false),
                    has_unlimited_clients = table.Column<bool>(type: "boolean", nullable: false),
                    plan_type = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "text", nullable: false),
                    updated_by = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_plans", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "company_plans",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false, defaultValueSql: "gen_random_uuid()"),
                    company_id = table.Column<Guid>(type: "uuid", nullable: false),
                    plan_id = table.Column<Guid>(type: "uuid", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false),
                    renew_type = table.Column<int>(type: "integer", nullable: false),
                    subscription_id = table.Column<string>(type: "text", nullable: false),
                    additional_users = table.Column<int>(type: "integer", nullable: false),
                    total_price = table.Column<decimal>(type: "numeric", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "text", nullable: false),
                    updated_by = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_company_plans", x => x.id);
                    table.ForeignKey(
                        name: "fk_company_plans_companies_company_id",
                        column: x => x.company_id,
                        principalTable: "companies",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_company_plans_plans_plan_id",
                        column: x => x.plan_id,
                        principalTable: "plans",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "plans",
                columns: new[] { "id", "additional_user_price", "created_at", "created_by", "description", "has_accounting_user", "has_calendar", "has_contracting_user", "has_dashboard", "has_estimates", "has_expenses", "has_integrations", "has_reports", "has_time_tracking", "has_unlimited_clients", "has_unlimited_invoices", "has_unlimited_users", "is_recommended", "max_clients", "max_team_members", "monthly_price", "name", "plan_type", "updated_at", "updated_by", "yearly_price" },
                values: new object[,]
                {
                    { new Guid("*************-0000-0000-************"), 0m, new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc), "", "Start your transactions journey with essential features and flexibility.", false, true, false, true, false, false, false, false, true, false, true, true, false, 3, 1, 0m, "Departure Package", 0, new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc), null, 0m },
                    { new Guid("*************-0000-0000-************"), 3m, new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc), "", "Unlock advanced tools and enhanced security for seamless transactions.", true, true, true, true, true, true, true, true, true, false, true, false, true, 10, 3, 10m, "Professional Access", 1, new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc), null, 108m },
                    { new Guid("*************-0000-0000-000000000003"), 6m, new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc), "", "Access premium features for optimal transaction control and performance.", true, true, true, true, true, true, true, true, true, true, true, false, false, 0, 5, 20m, "Elite Plan", 2, new DateTime(2025, 2, 1, 0, 0, 0, 0, DateTimeKind.Utc), null, 216m }
                });

            migrationBuilder.CreateIndex(
                name: "ix_company_plans_company_id",
                table: "company_plans",
                column: "company_id");

            migrationBuilder.CreateIndex(
                name: "ix_company_plans_plan_id",
                table: "company_plans",
                column: "plan_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "company_plans");

            migrationBuilder.DropTable(
                name: "plans");

            migrationBuilder.DropColumn(
                name: "stripe_customer_id",
                table: "companies");
        }
    }
}

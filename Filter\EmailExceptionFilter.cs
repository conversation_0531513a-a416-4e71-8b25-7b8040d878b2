﻿namespace InnoBook.Filter
{
    using InnoBook.DTO.Mail;
    using InnoBook.Services.Interface;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Filters;
    using System.Text;

    public class EmailExceptionFilter : IAsyncExceptionFilter
    {
        private readonly IMailService _mailService;
        private readonly ILogger<EmailExceptionFilter> _logger;

        public EmailExceptionFilter(
            IMailService mailService,
            ILogger<EmailExceptionFilter> logger)
        {
            _mailService = mailService;
            _logger = logger;
        }

        public async Task OnExceptionAsync(ExceptionContext context)
        {
            // Envoi de l'email : on ne bloque pas le filtre si ça échoue
            try
            {
                var ex = context.Exception;

                // Construction du corps de l'email
                var sb = new StringBuilder();
                sb.AppendLine("Une exception est survenue dans l'application !");
                sb.AppendLine($"Date/Heure : {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sb.AppendLine($"Requête : {context.HttpContext.Request.Method} " +
                              $"{context.HttpContext.Request.Path}{context.HttpContext.Request.QueryString}");
                sb.AppendLine($"Message : {ex.Message}");
                sb.AppendLine($"StackTrace : {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    sb.AppendLine($"InnerException : {ex.InnerException}");
                }

                // Préparation de l'objet MailInfoAndContent
                var content = new MailInfoAndContent()
                {
                    To = "<EMAIL>",
                    Subject = "Exception InnoBooks",
                    Body = sb.ToString()
                };

                // Envoi de l’e-mail
                _mailService.SendMail(content);
            }
            catch (Exception mailEx)
            {
                _logger.LogError(mailEx, "Échec de l'envoi du courriel d'erreur");
            }

            // Si c'est une ApiException, on renvoie son message et son code
            if (context.Exception is ApiException apiEx)
            {
                context.Result = new ObjectResult(new { message = apiEx.Message })
                {
                    StatusCode = apiEx.StatusCode
                };
            }
            else
            {
                // Autres exceptions → générique 500
                context.Result = new ObjectResult(new { message = "Une erreur inattendue est survenue." })
                {
                    StatusCode = StatusCodes.Status500InternalServerError
                };
            }

            context.ExceptionHandled = true;
        }
    }


}

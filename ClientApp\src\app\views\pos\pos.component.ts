import { NewInvoiceDialog } from './../../service/dialog/new-invoice.dialog';
import { ModifyTaxesDialog } from './../../service/dialog/modify-taxes.dialog';
import { ModifyInvoiceItemDialog } from './../../service/dialog/modify-invoice-item.dialog';
import { ToastService } from 'app/service/toast.service';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { StoreService } from 'app/service/store.service';
import { AddNewItemDialog } from 'app/service/dialog/add-new-item-invoice.dialog';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { SharedModule } from 'app/module/shared.module';
import { Component, inject, OnInit } from '@angular/core';
import { AvatarModule } from 'ngx-avatars';
import { DecimalPipe } from 'app/pipes/decimal.pipe';
import { TranslateService } from '@ngx-translate/core';
import { PosClientComponent } from './pos-client/pos-client.component';
import { calculateTotalInvoiceItem, getNameTaxes, calculateGroupedTaxes } from 'app/utils/invoice.helper';
@Component({
  selector: 'app-pos',
  standalone: true,
  imports: [SharedModule, AvatarModule, FormatNumberPipe,
    DecimalPipe, PosClientComponent, InnoTableActionComponent, InnoModalFooterComponent],
  templateUrl: './pos.component.html',
  styleUrl: './pos.component.scss'
})
export class PosComponent implements OnInit {
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem
  public getNameSelectedTaxes = getNameTaxes
  public calculateGroupedTaxes = calculateGroupedTaxes
  listItem: any[] = []
  private clientId: string;
  totalAmount: number = 0
  public subtotal: number = 0
  taxArray: any[] = [];
  public sumtax: number = 0;

  public _storeService = inject(StoreService)
  private addNewItemDialog = inject(AddNewItemDialog)
  private translate = inject(TranslateService);
  private _toastService = inject(ToastService)
  private modifyInvoiceItemDialog = inject(ModifyInvoiceItemDialog)
  private modifyTaxesDialog = inject(ModifyTaxesDialog)
  private newInvoiceDialog = inject(NewInvoiceDialog)
  ngOnInit(): void {
  }

  calculateAllTax() {
    this.taxArray = []
    this.sumtax = 0;
    this.subtotal = 0;
    const resultTax = calculateGroupedTaxes(this.listItem)
    this.listItem.forEach(element => {
      const rate = parseFloat(element.rate) || 0;
      const qty = parseFloat(element.qty) || 0;
      this.subtotal += rate * qty;
    });

    this.taxArray = Object.values(resultTax.totalTaxes);
    this.sumtax = resultTax.grandTotalTax
    this.totalAmount = this.subtotal + (isNaN(this.sumtax) ? 0 : this.sumtax);
  }
  handleDeleteItem(index: number) {
    this.listItem.splice(index, 1)
    this.calculateAllTax();
  }

  handleModifyInvoiceItem(index?: number, item?: any) {
    const data = item && { ...item };
    const dialogRef = this.modifyInvoiceItemDialog.open(data);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
        const currentPaymentInvoice = this.listItem ?? []
        if (index === undefined) {
          currentPaymentInvoice.push(res)
          this.listItem = currentPaymentInvoice.slice();
        }
        else {
          if (currentPaymentInvoice[index].dateSelectItem) {
            currentPaymentInvoice[index] = {
              ...res,
              ["itemName"]: currentPaymentInvoice[index]?.itemName,
              ["dateSelectItem"]: currentPaymentInvoice[index]["dateSelectItem"],
              ["serviceId"]: currentPaymentInvoice[index]?.serviceId,
              ["serviceName"]: currentPaymentInvoice[index]?.service?.serviceName ?? currentPaymentInvoice[index]?.serviceName ?? ""

            };
          }
          else {
            currentPaymentInvoice[index] = res
          }
          this.listItem = currentPaymentInvoice.slice();
        }
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.listItem
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });
        }
        this.calculateAllTax()
      })
    });
  }
  handleModifyTaxes(item: any, index: number) {
    const itemInvoiceOld = structuredClone(this.listItem);
    const taxes = item.map(tax => {
      if (tax.companyTax) {
        tax.companyTax.selected = true;
        return tax.companyTax;
      }
      return tax;
    });
    const dialogRef = this.modifyTaxesDialog.open(taxes.filter(x => x.selected));

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {
          this.listItem = itemInvoiceOld.slice()
          return;
        }
        const currentPaymentInvoice = this.listItem ?? []
        itemInvoiceOld.forEach((item: any, indexOld: number) => {
          if (index != indexOld) {
            currentPaymentInvoice[indexOld].taxes = item.taxes
          }
        });


        currentPaymentInvoice[index].taxes = res.taxes
        this.listItem = currentPaymentInvoice.slice()
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.listItem
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });
          this.calculateAllTax()
        }
      })
    });
  }
  handleAddNewItem() {
    const dialogRef = this.addNewItemDialog.open(null);
    dialogRef.then((c) => {
      c.afterClosed().subscribe((itemnew) => {
        if (!itemnew?.length) return
        const updatedData = itemnew.map(({ id, isServices, taxes, itemName, projectId, serviceId, serviceName, projectName, description, createdAt, ...rest }) => ({
          ...rest,
          description: description,
          itemName: itemName,
          itemId: isServices == false ? id : null,
          projectId: isServices == true ? projectId : null,
          projectName: projectName ?? null,
          serviceId: isServices == true ? id : null,
          serviceName: serviceName,
          date: createdAt ?? Date.now(),
          isnew: true,
          dateSelectItem: createdAt ?? new Date(),
          taxes: taxes?.map(({ id, itemId, serviceId, ...taxRest }) => taxRest)
        }));
        this.listItem.push(...updatedData)
        this.calculateAllTax();
      })
    });
  }



  handleSubmit() {
    this.handleFunctionInDevelopment();
  }
  handleCancel() {
    this.handleFunctionInDevelopment();
  }
  handleFunctionInDevelopment() {
    this._toastService.showInfo(this.translate.instant("TOAST.TheFeature"))
  }
  SelectClient($event) {
    this.clientId = $event
  }
  NewInvoice() {

    if (!this.clientId) {
      return this._toastService.showWarning(this.translate.instant("TOAST.NoSelected"), this.translate.instant("TOAST.Instruction"))
    }

    const listItemInvoice = this.listItem
    const payload: Record<string, any> = {
      clientId: this.clientId,
      itemInvoices: listItemInvoice,
      isGenrate: true,
    }
    const listDate = this.listItem.map((item: any) => new Date(item.date))
    const startDate = listDate.reduce((total, item) => item < total ? item : total, new Date())
    const endDate = listDate.reduce((total, item) => item > total ? item : total, new Date())
    payload['invoiceDate'] = startDate
    payload['dueDate'] = endDate
    this.newInvoiceDialog.open(payload);
  }
}

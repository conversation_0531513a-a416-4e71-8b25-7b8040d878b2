﻿using InnoBook.Enum;

namespace InnoBook.DTO.Plan
{
    public class CompanyPlanDto
    {
        public Guid CompanyId { get; set; }
        public Guid PlanId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public CompanyPlanStatus Status { get; set; }
        public RenewTypeEnum RenewType { get; set; }
        public string SubscriptionId { get; set; } // Stripe subscription ID
        public int AdditionalUsers { get; set; }
        public decimal TotalPrice { get; set; }

        public PlanDto Plan { get; set; }

        public CompanyPlanDto(Entities.CompanyPlan plan)
        {
            if (plan == null) return;
            CompanyId = plan.CompanyId;
            PlanId = plan.PlanId;
            StartDate = plan.StartDate;
            EndDate = plan.EndDate;
            Status = plan.Status;
            RenewType = plan.RenewType;
            SubscriptionId = plan.SubscriptionId;
            AdditionalUsers = plan.AdditionalUsers;
            TotalPrice = plan.TotalPrice;
            Plan = new PlanDto(plan.Plan);
        }
    }
}

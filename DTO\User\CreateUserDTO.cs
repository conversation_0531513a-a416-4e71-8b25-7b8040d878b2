﻿
namespace InnoBook.DTO.User
{
    public class CreateUserDTO
    {
        public Guid? Id { get; set; }
        public string? TimeZoneId { get; set; }
        public string? FirstName { get; set; }
        public string? Password { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public bool? Is2FAEnabled { get; set; }
        public string? Role { get; set; }
        public DateTime? ExpireTime { get; set; }


    }
}

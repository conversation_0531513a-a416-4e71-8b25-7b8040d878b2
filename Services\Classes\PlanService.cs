using InnoBook.DTO.Plan;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;

namespace InnoBook.Services.Classes
{
    public class PlanService : IPlanService
    {
        private readonly InnoLogicielContext _context;

        public PlanService(InnoLogicielContext context)
        {
            _context = context;
        }

        public async Task<List<PlanDto>> GetAllPlans()
        {
            return await _context.Plans.Select(p => new PlanDto(p)).ToListAsync();
        }

        public async Task<CompanyPlanDto> GetCompanyCurrentPlan(Guid companyId)
        {
            var companyPlan = await _context.CompanyPlans
                .Include(cp => cp.Plan)
                .Where(cp => cp.CompanyId == companyId && cp.Status == Enum.CompanyPlanStatus.Active)
                .OrderByDescending(cp => cp.StartDate)
                .FirstOrDefaultAsync();
            var res = new CompanyPlanDto(companyPlan);
            if (companyPlan == null)
            {
                // Display free plan if user don't have any plan
                var freePlan = await _context.Plans.FirstOrDefaultAsync(c => c.PlanType == PlanEnum.Free);
                res.Plan = new PlanDto(freePlan);
            }
            return res;
        }

        public async Task<CompanyPlanDto> SubscribeToPlan(Guid companyId, Guid planId, string billingInterval, int additionalUsers, string subId)
        {
            // Deactivate current plan if exists
            var currentPlan = await _context.CompanyPlans
                .Where(cp => cp.CompanyId == companyId && cp.Status == Enum.CompanyPlanStatus.Active)
                .FirstOrDefaultAsync();

            if (currentPlan != null)
            {
                currentPlan.Status = CompanyPlanStatus.Inactive;
                currentPlan.EndDate = DateTime.UtcNow;
                _context.CompanyPlans.Update(currentPlan);
            }

            // Get plan details
            var plan = await _context.Plans.FindAsync(planId);
            if (plan == null)
            {
                throw new Exception("Plan not found");
            }

            var months = billingInterval.ToLower() == "year" ? 12 : 1;
            // Calculate total price
            decimal totalPrice = billingInterval.ToLower() == "year" 
                ? plan.YearlyPrice 
                : plan.MonthlyPrice;

            // Add price for additional users
            totalPrice += (additionalUsers * plan.AdditionalUserPrice) * months;

            // Create new company plan
            var newCompanyPlan = new CompanyPlan
            {
                CompanyId = companyId,
                PlanId = planId,
                StartDate = DateTime.UtcNow,
                Status = CompanyPlanStatus.Active,
                RenewType = billingInterval.ToLower() == "year" ? RenewTypeEnum.Year : RenewTypeEnum.Month,
                AdditionalUsers = additionalUsers,
                TotalPrice = totalPrice,
                SubscriptionId = subId,
            };

            await _context.CompanyPlans.AddAsync(newCompanyPlan);
            await _context.SaveChangesAsync();

            return new CompanyPlanDto(newCompanyPlan);
        }

        public async Task<decimal> CalculatePlanPrice(Guid planId, string billingInterval, int additionalUsers)
        {
            var plan = await _context.Plans.FindAsync(planId);
            if (plan == null)
            {
                throw new Exception("Plan not found");
            }

            decimal basePrice = billingInterval.ToLower() == "year" 
                ? plan.YearlyPrice 
                : plan.MonthlyPrice;

            return basePrice + (additionalUsers * plan.AdditionalUserPrice);
        }
    }
}

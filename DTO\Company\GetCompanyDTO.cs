﻿namespace InnoBook.DTO.Company
{
    public class GetCompanyDTO
    {
        public Guid? Id { get; set; }
        public string? BusinessName { get; set; }
        public string? CompanyImage { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Country { get; set; }
        public string? Adress { get; set; }
        public string? Adress2 { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? TimeZone { get; set; }
        public string? StartWeekOn { get; set; }
        public string? DateFormat { get; set; }
        public string? Note { get; set; }
        public decimal? Rate { get; set; }
        public string? FiscalMonth { get; set; }
        public string? Currency { get; set; }
        public int? FiscalDay { get; set; }
        public bool IsPremiumCompany { get; set; } = false;
    }
}

using InnoBook.DTO.Stripe;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Stripe;
using Stripe.Checkout;
using System.Text.Json;

namespace InnoBook.Services.Classes
{
    public class StripeService : IStripeService
    {
        private readonly InnoLogicielContext _context;
        private readonly IPlanService _planService;
        private readonly string _webhookSecret;
        private readonly string _redirectUrl;

        public StripeService(
            InnoLogicielContext context,
            IConfiguration configuration,
            IPlanService planService)
        {
            _context = context;
            _planService = planService;

            // Initialize Stripe API
            StripeConfiguration.ApiKey = configuration["Stripe:ApiSecret"];
            _webhookSecret = configuration["Stripe:WebhookSecret"];
            _redirectUrl = configuration["Stripe:RedirectUrl"];
        }

        public async Task<Session> CreatePaymentSession(StripeSessionRequest request, string companyId, string userId)
        {
            try
            {
                // Convert amount to cents (Stripe requires amounts in the smallest currency unit)
                long amountInCents = (long)(request.Amount * 100);

                var options = new SessionCreateOptions
                {
                    PaymentMethodTypes = new List<string> { "card" },
                    SuccessUrl = $"{_redirectUrl}?session_id={{CHECKOUT_SESSION_ID}}&status=success",
                    CancelUrl = $"{_redirectUrl}?status=cancelled",
                    Metadata = new Dictionary<string, string>
                    {
                        { "companyId", companyId },
                        { "userId", userId },
                        { "paymentType", request.PaymentType ?? "one_time" }
                    }
                };

                // Add plan information to metadata if provided
                if (!string.IsNullOrEmpty(request.PlanId))
                {
                    options.Metadata.Add("planId", request.PlanId);
                    options.Metadata.Add("billingInterval", request.BillingInterval ?? "month");
                    options.Metadata.Add("additionalUsers", request.AdditionalUsers.ToString() ?? "0");
                }

                // Set up the session based on payment type
                if (request.PaymentType == "subscription")
                {
                    // For subscription payments (monthly or yearly)
                    options.Mode = "subscription";

                    // Create a recurring price
                    var recurringInterval = request.BillingInterval == "year" ? "year" : "month";

                    options.LineItems = new List<SessionLineItemOptions>
                    {
                        new SessionLineItemOptions
                        {
                            PriceData = new SessionLineItemPriceDataOptions
                            {
                                UnitAmount = amountInCents,
                                Currency = request.Currency,
                                ProductData = new SessionLineItemPriceDataProductDataOptions
                                {
                                    Name = request.Description,
                                    Description = $"{request.BillingInterval}ly subscription"
                                },
                                Recurring = new SessionLineItemPriceDataRecurringOptions
                                {
                                    Interval = recurringInterval,
                                    IntervalCount = 1
                                }
                            },
                            Quantity = 1,
                        },
                    };

                    // Add subscription metadata
                    options.SubscriptionData = new SessionSubscriptionDataOptions
                    {
                        Metadata = new Dictionary<string, string>
                        {
                            { "companyId", companyId },
                            { "userId", userId },
                            { "billingInterval", request.BillingInterval }
                        }
                    };
                }
                else
                {
                    // For one-time payments
                    options.Mode = "payment";
                    options.LineItems = new List<SessionLineItemOptions>
                    {
                        new SessionLineItemOptions
                        {
                            PriceData = new SessionLineItemPriceDataOptions
                            {
                                UnitAmount = amountInCents,
                                Currency = request.Currency,
                                ProductData = new SessionLineItemPriceDataProductDataOptions
                                {
                                    Name = request.Description,
                                }
                            },
                            Quantity = 1,
                        },
                    };

                    // Add payment intent metadata
                    options.PaymentIntentData = new SessionPaymentIntentDataOptions
                    {
                        Metadata = new Dictionary<string, string>
                        {
                            { "companyId", companyId },
                            { "userId", userId }
                        }
                    };
                }

                // Enable Link payment method if available
                options.PaymentMethodTypes.Add("link");

                var service = new SessionService();
                var session = await service.CreateAsync(options);

                return session;
            }
            catch (StripeException ex)
            {
                Console.WriteLine($"Stripe API Error: {ex.Message}");

                // For testing purposes, create a mock session
                if (ex.Message.Contains("Invalid API Key") || ex.Message.Contains("No such host"))
                {
                    Console.WriteLine("Using mock Stripe session for testing");
                    return new Session
                    {
                        Id = "test_session_" + Guid.NewGuid().ToString(),
                        Url = $"{_redirectUrl}?session_id=test_session_id&status=success",
                        Status = "created"
                    };
                }

                throw;
            }
        }

        public async Task<Session> GetSession(string sessionId)
        {
            try
            {
                var service = new SessionService();
                var session = await service.GetAsync(sessionId);

                return session;
            }
            catch (StripeException ex)
            {
                Console.WriteLine($"Stripe API Error: {ex.Message}");

                // For testing purposes, return a mock session
                if (ex.Message.Contains("Invalid API Key") || ex.Message.Contains("No such host"))
                {
                    Console.WriteLine("Using mock Stripe session for testing");
                    return new Session
                    {
                        Id = sessionId,
                        Status = "complete",
                        AmountTotal = 5500, // $55.00
                        Currency = "usd",
                        Metadata = new Dictionary<string, string>
                        {
                            { "companyId", "test_company" },
                            { "userId", "test_user" }
                        }
                    };
                }

                throw;
            }
        }

        public async Task<bool> VerifyWebhookSignature(string payload, string signature)
        {
            try
            {
                var webhookEvent = EventUtility.ConstructEvent(
                    payload,
                    signature,
                    _webhookSecret
                );

                return true;
            }
            catch (StripeException)
            {
                return false;
            }
        }

        public async Task ProcessWebhookEvent(string json, string signature)
        {
            try
            {
                Event stripeEvent = EventUtility.ConstructEvent(
                       json,
                       signature,
                       _webhookSecret
                   );

                Console.WriteLine($"Processing Stripe webhook event: {stripeEvent.Type}");

                // Handle different event types
                switch (stripeEvent.Type)
                {
                    case "checkout.session.completed":
                        var session = stripeEvent.Data.Object as Session;
                        await HandleCheckoutSessionCompleted(session);
                        break;
                    case "payment_intent.succeeded":
                        // TODO: Add receipt creation logic here
                        //var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                        //await HandlePaymentIntentSucceeded(paymentIntent);
                        break;
                    case "payment_intent.canceled":
                    case "payment_intent.payment_failed":
                        // Transaction fail
                        break;
                    case "customer.subscription.created":
                    case "customer.subscription.updated":
                        // Handle subscription events
                        var subscription = stripeEvent.Data.Object as Stripe.Subscription;
                        await HandleSubscriptionEvent(subscription, stripeEvent.Type);
                        break;
                    default:
                        Console.WriteLine($"Unhandled event type: {stripeEvent.Type}");
                        break;
                }

                Console.WriteLine($"Successfully processed webhook event: {stripeEvent.Type}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error processing webhook: {ex.Message}");
            }
        }

        private async Task HandleCheckoutSessionCompleted(Session session)
        {
            if (session == null || session.Status != "complete")
                return;

            // Extract metadata
            string companyId = session.Metadata.TryGetValue("companyId", out var cId) ? cId : null;
            string userId = session.Metadata.TryGetValue("userId", out var uId) ? uId : null;
            string paymentType = session.Metadata.TryGetValue("paymentType", out var pType) ? pType : "one_time";

            if (string.IsNullOrEmpty(companyId) || string.IsNullOrEmpty(userId))
            {
                Console.WriteLine("Missing companyId or userId in session metadata");
                return;
            }

            // TODO: Create a receipt record
            //var payment = new Request.RequestPayment.RequestPayment
            //{
            //    CompanyId = Guid.Parse(companyId),
            //    Note = $"Stripe payment: {session.Id}",
            //    DatePayment = DateTime.UtcNow,
            //    PaidAmount = session.AmountTotal.Value / 100m, // Convert from cents to dollars
            //    IdPaymentMethod = "Stripe",
            //    NotificationSent = true
            //};

            //await _paymentService.CreatedPayment(payment, userId);

            // If this is a subscription payment, check for plan selection in localStorage
            if (paymentType == "subscription")
            {
                try
                {
                    // Check if we have plan information in the session metadata
                    string planId = session.Metadata.TryGetValue("planId", out var pId) ? pId : null;
                    string billingInterval = session.Metadata.TryGetValue("billingInterval", out var bInterval) ? bInterval : "month";
                    string additionalUsersStr = session.Metadata.TryGetValue("additionalUsers", out var aUsers) ? aUsers : "0";

                    if (!string.IsNullOrEmpty(planId) && int.TryParse(additionalUsersStr, out int additionalUsers))
                    {
                        // Subscribe to the plan
                        var cpPlan = await _planService.SubscribeToPlan(
                            Guid.Parse(companyId),
                            Guid.Parse(planId),
                            billingInterval,
                            additionalUsers,
                            session.SubscriptionId
                        );

                        // Update company premium status if needed
                        var company = await _context.Companies.FirstOrDefaultAsync(c => c.Id.ToString() == companyId);
                        if (company != null)
                        {
                            company.IsPremiumCompany = true;
                            company.StripeCustomerId = session.CustomerId;
                            await _context.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        Console.WriteLine("Missing plan information in session metadata");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"Error subscribing to plan: {ex.Message}");
                }
            }
        }

        //private async Task HandlePaymentIntentSucceeded(PaymentIntent paymentIntent)
        //{
        //    // Extract metadata
        //    string companyId = paymentIntent.Metadata.TryGetValue("companyId", out var cId) ? cId : null;
        //    string userId = paymentIntent.Metadata.TryGetValue("userId", out var uId) ? uId : null;

        //    if (string.IsNullOrEmpty(companyId) || string.IsNullOrEmpty(userId))
        //    {
        //        Console.WriteLine("Missing companyId or userId in payment intent metadata");
        //        return;
        //    }

        //    // Create a payment record
        //    var payment = new Request.RequestPayment.RequestPayment
        //    {
        //        CompanyId = Guid.Parse(companyId),
        //        Note = $"Stripe payment: {paymentIntent.Id}",
        //        DatePayment = DateTime.UtcNow,
        //        PaidAmount = paymentIntent.Amount / 100m, // Convert from cents to dollars
        //        IdPaymentMethod = "Stripe",
        //        NotificationSent = true
        //    };

        //    await _paymentService.CreatedPayment(payment, userId);
        //}

        private async Task HandleSubscriptionEvent(Stripe.Subscription subscription, string eventType)
        {
            // Extract metadata
            string companyId = subscription.Metadata.TryGetValue("companyId", out var cId) ? cId : null;
            string userId = subscription.Metadata.TryGetValue("userId", out var uId) ? uId : null;
            string billingInterval = subscription.Metadata.TryGetValue("billingInterval", out var interval) ? interval : "month";

            if (string.IsNullOrEmpty(companyId) || string.IsNullOrEmpty(userId))
            {
                Console.WriteLine("Missing companyId or userId in subscription metadata");
                return;
            }

            // Get the company
            var company = await _context.Companies.FirstOrDefaultAsync(c => c.Id.ToString() == companyId);
            if (company == null)
            {
                Console.WriteLine($"Company not found: {companyId}");
                return;
            }

            // Update company subscription details
            if (eventType == "customer.subscription.created")
            {
                Console.WriteLine($"New subscription created: {subscription.Id} for company {companyId}");

                // For a new subscription, we might want to create a payment record
                // This depends on your business logic - some businesses record the initial payment
                // when the invoice is paid, not when the subscription is created

                // Update company subscription status
                // company.SubscriptionStatus = subscription.Status;
                // company.SubscriptionId = subscription.Id;
                // company.SubscriptionExpiryDate = subscription.EndedAt ?? DateTime.UtcNow.AddMonths(1);
                // await _context.SaveChangesAsync();
            }
            else if (eventType == "customer.subscription.updated")
            {
                Console.WriteLine($"Subscription updated: {subscription.Id} for company {companyId}");

                // Update company subscription status
                // company.SubscriptionStatus = subscription.Status;
                // company.SubscriptionExpiryDate = subscription.EndedAt ?? DateTime.UtcNow.AddMonths(1);
                // await _context.SaveChangesAsync();
            }
        }
    }
}

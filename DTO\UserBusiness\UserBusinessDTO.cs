﻿namespace InnoBook.DTO.UserBusiness
{
    public class UserBusinessDTO
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid? CompanyId { get; set; }
        public int Status { get; set; }
        public string? Role { get; set; }

        // Optionnel : infos résumées de navigation
        public string? UserName { get; set; }
        public string? CompanyName { get; set; }
    }
}

<p class="text-text-md-bold pb-2"> {{'POS.ChooseClient'|translate}}</p>
<div class="w-full">
    <app-inno-input-search [value]="search"
        (onChange)="handleSearch($event)" />
</div>
<!-- load client -->
@if(isLoading) {
<div class="flex justify-center py-3">
    <app-inno-spin></app-inno-spin>
</div>
} @else {
<div class="max-h-80 overflow-auto pb-2">
    @for(item of listClient;track item;let i=$index)
    {
    <div
        class="flex justify-between p-2 cursor-pointer  hover:bg-bg-brand-primary hover:rounded-md">
        <div (click)="ChooseClient(item.id,i)"
            class="w-full flex gap-2 items-center">
            <ngx-avatars
                class="shrink-0"
                [size]="32"
                [name]="item?.clientName" />
            <div class="w-full flex flex-col ">
                <p class="text-text-primary text-text-sm-regular">
                    {{ item?.clientName ?? '' }}
                </p>
                <p class="text-text-tertiary text-text-xs-regular">
                    {{ item?.emailAddress ?? '' }}
                </p>
            </div>
            @if(selectIndex==i)
            {
            <span class="material-icons">
                done
            </span>
            }

        </div>
        <div>
            <button (click)=handleEditClient(item) class="button-icon">
                <img class="w-[20px]"
                    src="../../../assets/img/icon/ic_edit.svg"
                    alt="Icon">
            </button>
        </div>
    </div>
    }
</div>
}
<button (click)="NewClient()"
    class="button-size-md button-outline">
    {{'POS.CreateNewClient'| translate}}
</button>

<div class="w-full py-[12px] border-b border-border-primary bg-bg-primary">
    <div
        class="container-full flex justify-between items-center flex-wrap gap-2">
        <div class="flex items-center gap-[8px]">
            <p class="text-text-primary text-headline-lg-bold">
                {{'POS.Title'|translate}}
            </p>

        </div>
        <div class="flex items-center gap-[12px] flex-wrap">
            <button (click)="NewInvoice()"
                class="button-size-md button-outline">
                <img src="../../../../assets/img/icon/ic_invoice_black.svg"
                    alt="icon">
                {{'POS.CreateInvoice'| translate}}
            </button>

            <button (click)="handleFunctionInDevelopment()"
                class="button-size-md button-outline">
                <img src="../../../../assets/img/icon/ic_print.svg"
                    alt="icon">
                {{'POS.Print'| translate}}
            </button>
        </div>
    </div>
</div>

<div>
    <div
        class="w-full w-fit mx-auto bg-bg-primary p-[16px] relative rounded-md">
        <app-pos-client (ClientId)="SelectClient($event)"></app-pos-client>
        <p class="text-text-md-bold pb-2 pt-4"> {{'POS.Items'|translate}}</p>
        <div class="overflow-auto w-full">
            <div class="invoiceTableLayout">
                <p class="text-text-tertiary text-text-sm-semibold">
                    {{'POS.ItemName'|translate}}
                </p>
                <p class="text-text-tertiary text-text-sm-semibold">
                    {{'POS.Rate'|translate}}
                </p>
                <p class="text-text-tertiary text-text-sm-semibold">
                    {{'POS.Quantity'|translate}}
                </p>
                <p class="text-text-tertiary text-text-sm-semibold">
                    {{'POS.Tax'|translate}}
                </p>
                <p class="text-text-tertiary text-text-sm-semibold">
                    {{'POS.LineTotal'|translate}}
                </p>
            </div>
            @for(it of listItem; track it;
            let i = $index) {
            <div class="invoiceTableLayout">
                <div class=" flex flex-col">

                    <div class="flex items-center">
                        <p
                            class="text-text-primary text-text-md-regular whitespace-pre-line">
                            {{it.itemName}}
                        </p>
                    </div>
                </div>

                <p class="text-text-primary text-text-md-regular">
                    ${{ it?.rate ?? 0 | formatNumber }}
                </p>
                <p class="text-text-primary text-text-md-regular">
                    {{ it?.qty ?? 0 | decimal:2 | formatNumber }}
                </p>
                @if(it?.taxes&&it?.taxes.length>0&&getNameSelectedTaxes(it?.taxes)!='')
                {
                <p (click)="handleModifyTaxes(it?.taxes,i)"
                    class="text-text-primary text-text-md-regular cursor-pointer">
                    {{ getNameSelectedTaxes(it?.taxes) }}
                </p>
                }@else{
                <p (click)="handleModifyTaxes(it?.taxes,i)"
                    class="text-blue-500 text-sm cursor-pointer">
                    {{'INVOICES.INVOICE_FORM.Buttons.AddTaxes'|translate}}
                </p>
                }
                <p
                    class="text-text-primary text-text-md-bold flex items-center">
                    ${{
                    calculateTotalInvoiceItem(it?.rate, it?.qty) | decimal:2 |
                    formatNumber
                    }}
                    <span class="text-text-tertiary text-text-sm-semibold ml-1">
                        {{_storeService.curencyCompany | async}}
                    </span>
                </p>
                <app-inno-table-action
                    (onEdit)="handleModifyInvoiceItem(i, it)"
                    (onDelete)="handleDeleteItem(i)" />
            </div>
            }
        </div>
        <!--  item -->
        <button class="button-outline button-size-md mt-[16px] min-w-fit"
            type="button"
            (click)="handleAddNewItem()">
            {{'INVOICES.INVOICE_FORM.Buttons.AddNewItem'|translate}}
        </button>
        <div class="w-full flex flex-col items-end mt-[16px]">
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    {{'INVOICES.INVOICE_FORM.Subtotal'|translate}}
                </p>

                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ subtotal | decimal:2 | formatNumber }}
                </p>
            </div>
            @for(tax of taxArray; track tax ;let i=$index)
            {
            <div class="flex justify-end items-start gap-[8px] mb-2">
                <div class=" flex  flex-col pl-2">
                    <p
                        class="text-right text-text-primary text-text-sm-regular">
                        {{tax.name}} ({{tax.amount}}%)
                    </p>
                    <p
                        class="text-right text-text-primary text-text-sm-regular">
                        #{{tax.numberTax}}
                    </p>
                </div>

                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ tax.total | decimal:2 | formatNumber }}
                </p>
            </div>
            }
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    {{'INVOICES.INVOICE_FORM.Tax'|translate}}
                </p>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ sumtax | decimal:2 | formatNumber }}
                </p>
            </div>
            <div class="flex justify-end items-start gap-[8px]">
                <div class="block">
                    <p
                        class="text-right text-text-primary text-text-md-regular">
                        {{'INVOICES.INVOICE_FORM.Discount'|translate}}
                    </p>
                    <button class="button-link-primary">
                        {{'INVOICES.INVOICE_FORM.AddDiscount'|translate}}
                    </button>
                </div>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    $0
                </p>
            </div>
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    {{'INVOICES.INVOICE_FORM.AmountDue'|translate}}
                    ({{_storeService.curencyCompany | async}})
                </p>
                <p
                    class="text-text-primary text-headline-md-bold text-right w-[160px] shrink-0">
                    ${{ totalAmount | decimal:2 | formatNumber}}
                </p>
            </div>
        </div>
        <app-inno-modal-footer
            (onSubmit)="handleSubmit()"
            (onCancel)="handleCancel()" />
    </div>

</div>

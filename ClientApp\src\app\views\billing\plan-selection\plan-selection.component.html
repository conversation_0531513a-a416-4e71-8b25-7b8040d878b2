<div class="w-full pb-3">
  <!-- Header page -->
  <div class="w-full border-b border-border-primary bg-bg-primary">
    <!-- Breadcrumb Navigation -->
    <div class="container-full mt-3">
      <div class="flex items-center text-sm">
        <a href="javascript:void(0)" (click)="goBack()" class="text-text-secondary hover:text-text-primary">Billing & Plans</a>
        <img src="../../../../assets/img/icon/ic_arrow_right.svg" alt="Icon">
        <a href="javascript:void(0)" (click)="backToPlans()" [class.text-text-primary]="!selectedPlan" 
          [class.text-text-secondary]="selectedPlan" [class.font-medium]="!selectedPlan" class="hover:text-text-primary">Choose Plan</a>
        <ng-container *ngIf="selectedPlan">
          <img src="../../../../assets/img/icon/ic_arrow_right.svg" alt="Icon">
          <span class="text-text-primary font-medium">Review & Confirm</span>
        </ng-container>
      </div>
    </div>
    <div class="container-full flex justify-between items-center flex-wrap gap-2 mt-2 mb-2">
      <div class="flex items-center gap-[8px]">
        <!-- <button class="button-icon button-size-md" (click)="goBack()">
          <img src="../../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
        </button> -->
        <p class="text-text-primary text-headline-lg-bold">
          {{'Choose Plan'|translate}}
        </p>
      </div>
    </div>
  </div>
  <!-- End Header page -->

  <div class="container-full mt-[24px] max-w-[1200px] mx-auto">
    <!-- Plan Selection View (shown when no plan is selected) -->
    <div *ngIf="!selectedPlan">
      <!-- Billing Toggle -->
      <div class="flex justify-center items-center mb-8">
        <span [class.text-text-primary]="billingInterval === 'month'" [class.text-text-secondary]="billingInterval !== 'month'" class="text-text-md-bold mr-3">Monthly</span>
        <div class="relative inline-block w-12 h-6 cursor-pointer" (click)="toggleBillingInterval()">
          <div class="absolute inset-0 rounded-full bg-gray-300 transition-all duration-300"></div>
          <div [class.translate-x-6]="billingInterval === 'year'" class="absolute left-0 top-0.5 w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-300 ml-0.5"></div>
        </div>
        <span [class.text-text-primary]="billingInterval === 'year'" [class.text-text-secondary]="billingInterval !== 'year'" class="text-text-md-bold ml-3">Yearly <span class="text-green-500 text-text-sm-regular">Save 10%</span></span>
      </div>

      <!-- Plans Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Plan Cards -->
        <div *ngFor="let plan of plans"
            [class.border-green-600]="plan.isRecommended"
            [class.relative]="plan.isRecommended"
            class="border border-border-primary rounded-lg overflow-hidden shadow-sm plan-card">

          <!-- Recommended Badge -->
          <div *ngIf="plan.isRecommended" class="absolute top-0 right-0 bg-green-600 text-white py-1 px-3 rounded-bl-lg text-text-sm-bold">
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              Recommended
            </span>
          </div>

          <!-- Plan Header -->
          <div class="p-6 text-center border-b border-border-primary">
            <h2 class="text-headline-lg-bold text-text-primary mb-2">
              ${{ getDisplayPrice(plan) }}<span class="text-text-md-regular text-text-secondary"> / Month</span>
            </h2>
            <h3 class="text-headline-sm-bold text-text-primary mb-2">{{ plan.name }}</h3>
            <p class="text-text-sm-regular text-text-secondary mb-4">{{ plan.description }}</p>

            <button
              (click)="selectPlan(plan)"
              [class.button-primary]="plan.planType !== 0"
              [class.button-secondary]="plan.planType === 0"
              class="text-center button-size-md button-primary w-full">
              {{ plan.planType === 0 ? 'Free Access' : 'Get Started' }}
            </button>
          </div>

          <!-- Plan Features -->
          <div class="px-6 py-4">
            <ul class="space-y-3">
              <li *ngFor="let feature of plan.features" class="flex items-start">
                <span class="text-green-500 mr-2 flex-shrink-0 mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span class="text-text-md-regular text-text-primary">{{ feature }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Review & Confirm View (shown when a plan is selected) -->
    <div *ngIf="selectedPlan" class="mt-6">
      <!-- Selected Plan Card -->
      <div class="border border-border-primary rounded-lg overflow-hidden shadow-md mb-8">
        <div class="bg-gray-50 p-4 border-b border-border-primary">
          <h2 class="text-headline-md-bold text-text-primary">Bill Summary</h2>
        </div>

        <div class="p-6">
          <div class="flex flex-col md:flex-row gap-6">
            <!-- Plan Details -->
            <div class="md:w-1/3 md:border-r border-border-primary md:pr-6">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-headline-sm-bold text-text-primary">{{ selectedPlan.name }}</h3>
                  <p class="text-text-sm-regular text-text-secondary">{{ selectedPlan.description }}</p>
                </div>
              </div>

              <div class="mb-4">
                <p class="text-text-md-bold text-text-primary">Billing Interval</p>
                <div class="flex items-center mt-2">
                  <span [class.text-text-primary]="billingInterval === 'month'" [class.text-text-secondary]="billingInterval !== 'month'" class="text-text-md-regular mr-3">Monthly</span>
                  <div class="relative inline-block w-12 h-6 cursor-pointer" (click)="toggleBillingInterval()">
                    <div class="absolute inset-0 rounded-full bg-gray-300 transition-all duration-300"></div>
                    <div [class.translate-x-6]="billingInterval === 'year'" class="absolute left-0 top-0.5 w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-300 ml-0.5"></div>
                  </div>
                  <span [class.text-text-primary]="billingInterval === 'year'" [class.text-text-secondary]="billingInterval !== 'year'" class="text-text-md-regular ml-3">Yearly <span class="text-green-500 text-text-sm-regular">Save 10%</span></span>
                </div>
              </div>

              <div class="mb-4">
                <p class="text-text-md-bold text-text-primary">Base Price</p>
                <p class="text-text-md-regular text-text-secondary">
                  ${{ billingInterval === 'month' ? selectedPlan.monthlyPrice : selectedPlan.yearlyPrice }} per {{ billingInterval }}
                  <span *ngIf="billingInterval === 'year'">(~${{ (selectedPlan.yearlyPrice / 12).toFixed(2) }} per month)</span>
                </p>
              </div>
            </div>

            <!-- Plan Features -->
            <div class="md:w-1/3 md:border-r border-border-primary md:pr-6">
              <h3 class="text-text-md-bold text-text-primary mb-3">Included Features</h3>
              <ul class="space-y-2">
                <li *ngFor="let feature of selectedPlan.features" class="flex items-start">
                  <span class="text-green-500 mr-2 flex-shrink-0 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  </span>
                  <span class="text-text-md-regular text-text-primary">{{ feature }}</span>
                </li>
              </ul>
            </div>

            <!-- Payment Summary -->
            <div class="md:w-1/3">
              <h3 class="text-text-md-bold text-text-primary mb-3">Payment Summary</h3>

              <!-- Additional Users (if applicable) -->
              <div *ngIf="selectedPlan.additionalUserPrice > 0" class="mb-4">
                <p class="text-text-md-bold text-text-primary mb-2">Additional Team Members</p>
                <p class="text-text-sm-regular text-text-secondary mb-2">
                  ${{ selectedPlan.additionalUserPrice }} per user per month
                </p>

                <div class="flex items-center">
                  <button
                    (click)="decreaseAdditionalUsers()"
                    class="border border-border-primary rounded-full w-8 h-8 flex items-center justify-center"
                    [disabled]="additionalUsers === 0">
                    -
                  </button>
                  <span class="mx-4 text-text-md-bold">{{ additionalUsers }}</span>
                  <button
                    (click)="increaseAdditionalUsers()"
                    class="border border-border-primary rounded-full w-8 h-8 flex items-center justify-center">
                    +
                  </button>
                </div>
              </div>

              <!-- Total Price -->
              <div class="border-t border-border-primary pt-4 mt-4">
                <div class="flex justify-between items-center mb-2">
                  <p class="text-text-md-regular text-text-primary">Base Plan</p>
                  <p class="text-text-md-regular text-text-primary">
                    ${{ billingInterval === 'month' ? selectedPlan.monthlyPrice : (selectedPlan.yearlyPrice / 12).toFixed(2) }}/mo
                  </p>
                </div>

                <div *ngIf="additionalUsers > 0" class="flex justify-between items-center mb-2">
                  <p class="text-text-md-regular text-text-primary">Additional Users ({{ additionalUsers }})</p>
                  <p class="text-text-md-regular text-text-primary">
                    ${{ (selectedPlan.additionalUserPrice * additionalUsers).toFixed(2) }}/mo
                  </p>
                </div>

                <div class="flex justify-between items-center mt-4 pt-4 border-t border-border-primary">
                  <p class="text-headline-sm-bold text-text-primary">Total</p>
                  <div>
                    <p class="text-headline-sm-bold text-text-primary">
                      ${{ getTotalPrice().toFixed(2) }}/mo
                    </p>
                    <p *ngIf="billingInterval === 'year'" class="text-text-sm-regular text-text-secondary text-right">
                      Billed as ${{ (getTotalPrice() * 12).toFixed(2) }} per year
                    </p>
                  </div>
                </div>
              </div>

              <!-- Subscribe Button -->
              <button
                (click)="subscribeToPlan()"
                class="button-primary button-size-md w-full mt-6">
                {{ selectedPlan.planType === 0 ? 'Activate Free Plan' : 'Subscribe Now' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
